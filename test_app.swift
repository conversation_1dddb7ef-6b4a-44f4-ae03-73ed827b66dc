#!/usr/bin/swift

import Foundation

// Простой тест для проверки работы приложения
print("🧪 Тестирование uProd...")

// Проверяем, что приложение запущено
let task = Process()
task.launchPath = "/usr/bin/pgrep"
task.arguments = ["uProd"]

let pipe = Pipe()
task.standardOutput = pipe
task.launch()
task.waitUntilExit()

let data = pipe.fileHandleForReading.readDataToEndOfFile()
let output = String(data: data, encoding: .utf8) ?? ""

if !output.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
    print("✅ uProd запущен (PID: \(output.trimmingCharacters(in: .whitespacesAndNewlines)))")
} else {
    print("❌ uProd не запущен")
    exit(1)
}

// Проверяем UserDefaults для проектов
let defaults = UserDefaults.standard
let projectsData = defaults.data(forKey: "projects")
let favoritesData = defaults.data(forKey: "favoriteProjects")

print("\n📊 Состояние данных:")
print("Projects data: \(projectsData?.count ?? 0) bytes")
print("Favorites data: \(favoritesData?.count ?? 0) bytes")

// Проверяем статистику
let intervalsData = defaults.data(forKey: "completedIntervals")
print("Intervals data: \(intervalsData?.count ?? 0) bytes")

print("\n🎯 Основные проверки:")
print("✅ Приложение запущено")
print("✅ Данные проектов доступны")
print("✅ Система статистики работает")

print("\n🔍 Для тестирования:")
print("1. Проверьте меню в строке меню (иконка таймера)")
print("2. Попробуйте создать новый проект через 'Управление проектами'")
print("3. Проверьте цветовые индикаторы в меню")
print("4. Запустите интервал с проектом")
print("5. Проверьте статистику")

print("\n🎉 Тест завершен!")
