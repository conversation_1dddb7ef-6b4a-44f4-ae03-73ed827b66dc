#!/usr/bin/swift

import Cocoa
import CoreGraphics

func createEmojiIcon(size: CGFloat, emoji: String, filename: String) -> <PERSON><PERSON> {
    // Создаем изображение
    let image = NSImage(size: NSSize(width: size, height: size))
    
    image.lockFocus()
    
    // Очищаем фон (прозрачный)
    NSColor.clear.set()
    NSRect(x: 0, y: 0, width: size, height: size).fill()
    
    // Настраиваем шрифт для эмодзи - используем Apple Color Emoji
    let fontSize = size * 0.8  // Увеличиваем размер эмодзи
    let font = NSFont(name: "Apple Color Emoji", size: fontSize) ?? NSFont.systemFont(ofSize: fontSize)
    
    // Создаем атрибуты текста
    let attributes: [NSAttributedString.Key: Any] = [
        .font: font,
        .foregroundColor: NSColor.black
    ]
    
    // Создаем атрибутированную строку
    let attributedString = NSAttributedString(string: emoji, attributes: attributes)
    
    // Вычисляем размер текста
    let textSize = attributedString.size()
    
    // Позиционируем по центру
    let x = (size - textSize.width) / 2
    let y = (size - textSize.height) / 2
    
    // Рисуем текст
    attributedString.draw(at: NSPoint(x: x, y: y))
    
    image.unlockFocus()
    
    // Сохраняем как PNG
    guard let tiffData = image.tiffRepresentation,
          let bitmapRep = NSBitmapImageRep(data: tiffData),
          let pngData = bitmapRep.representation(using: .png, properties: [:]) else {
        print("❌ Не удалось создать PNG данные для \(filename)")
        return false
    }
    
    do {
        try pngData.write(to: URL(fileURLWithPath: filename))
        print("✅ Создана эмодзи иконка: \(filename)")
        return true
    } catch {
        print("❌ Не удалось сохранить: \(filename) - \(error)")
        return false
    }
}

// Основная функция
func main() {
    print("🍅 Создаем иконки uProd из эмодзи помидора...")
    
    let iconDir = "SimplePomodoroTest/Assets.xcassets/AppIcon.appiconset"
    let emoji = "🍅"
    
    // Размеры иконок для macOS с правильными именами файлов
    let sizes: [(size: CGFloat, name: String)] = [
        (16, "icon_16x16.png"),
        (32, "<EMAIL>"),
        (32, "icon_32x32.png"),
        (64, "<EMAIL>"),
        (128, "icon_128x128.png"),
        (256, "<EMAIL>"),
        (256, "icon_256x256.png"),
        (512, "<EMAIL>"),
        (512, "icon_512x512.png"),
        (1024, "<EMAIL>")
    ]
    
    var successCount = 0
    
    for (size, name) in sizes {
        let filename = "\(iconDir)/\(name)"
        if createEmojiIcon(size: size, emoji: emoji, filename: filename) {
            successCount += 1
        }
    }
    
    print("✅ Создано \(successCount) из \(sizes.count) эмодзи иконок")
    print("🔄 Теперь пересоберите проект для применения новых иконок")
}

main()
