#!/usr/bin/swift

import Foundation

print("🔧 Принудительное создание проектов по умолчанию...")

// Структуры для создания проектов
struct ProjectData: Codable {
    let id: UUID
    let name: String
    let type: ProjectTypeData
    let parentId: UUID?
    let color: String?
    let isActive: Bool
    let isArchived: Bool
    let isWorkRelated: Bool
    let createdAt: Date
    let lastUsedAt: Date?
    
    init(name: String, type: ProjectTypeData, color: String, isWorkRelated: Bool) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.parentId = nil
        self.color = color
        self.isActive = true
        self.isArchived = false
        self.isWorkRelated = isWorkRelated
        self.createdAt = Date()
        self.lastUsedAt = nil
    }
}

enum ProjectTypeData: String, Codable, CaseIterable {
    case work = "work"
    case personal = "personal"
    case learning = "learning"
    case health = "health"
    case creative = "creative"
    
    var emoji: String {
        switch self {
        case .work: return "💼"
        case .personal: return "🏠"
        case .learning: return "📚"
        case .health: return "💪"
        case .creative: return "🎨"
        }
    }
    
    var displayName: String {
        switch self {
        case .work: return "Работа"
        case .personal: return "Личное"
        case .learning: return "Обучение"
        case .health: return "Здоровье"
        case .creative: return "Творчество"
        }
    }
}

// Создаем проекты по умолчанию
let defaultProjects = [
    ProjectData(name: "Работа", type: .work, color: "#3B82F6", isWorkRelated: true),
    ProjectData(name: "Личные дела", type: .personal, color: "#10B981", isWorkRelated: false),
    ProjectData(name: "Обучение", type: .learning, color: "#8B5CF6", isWorkRelated: false)
]

// Сохраняем в UserDefaults
let defaults = UserDefaults.standard

do {
    let encoder = JSONEncoder()
    encoder.dateEncodingStrategy = .iso8601
    
    let projectsData = try encoder.encode(defaultProjects)
    defaults.set(projectsData, forKey: "projects")
    
    // Создаем список избранного (все проекты по умолчанию)
    let favoriteIds = defaultProjects.map { $0.id }
    let favoritesData = try encoder.encode(favoriteIds)
    defaults.set(favoritesData, forKey: "favoriteProjects")
    
    defaults.synchronize()
    
    print("✅ Создано проектов: \(defaultProjects.count)")
    for project in defaultProjects {
        let workType = project.isWorkRelated ? "💼" : "🏠"
        print("   - \(project.type.emoji) \(project.name) \(workType) (\(project.color ?? "без цвета"))")
    }
    
    print("✅ Добавлено в избранное: \(favoriteIds.count) проектов")
    print("✅ Данные сохранены в UserDefaults")
    
} catch {
    print("❌ Ошибка при создании проектов: \(error)")
}

print("\n🎯 Теперь попробуйте:")
print("1. Перезапустить приложение")
print("2. Проверить меню в строке меню")
print("3. Должны появиться проекты с цветовыми индикаторами")
print("4. Попробовать создать новый проект через 'Управление проектами'")

print("\n🎉 Готово!")
