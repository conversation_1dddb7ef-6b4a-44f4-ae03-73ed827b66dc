#!/usr/bin/swift

import Foundation

print("🎯 Финальное тестирование новых функций uProd...")

// Структуры для декодирования
struct ProjectData: Codable {
    let id: UUID
    let name: String
    let type: ProjectTypeData
    let isWorkRelated: Bool
    let color: String?
    let isActive: Bool
    let isArchived: Bool
    let createdAt: Date
    let lastUsedAt: Date?
}

enum ProjectTypeData: String, Codable {
    case work = "work"
    case personal = "personal"
    case learning = "learning"
    case health = "health"
    case creative = "creative"
    
    var emoji: String {
        switch self {
        case .work: return "💼"
        case .personal: return "🏠"
        case .learning: return "📚"
        case .health: return "💪"
        case .creative: return "🎨"
        }
    }
}

// Проверяем данные проектов
func testProjectData() {
    print("\n📊 Тестирование данных проектов:")
    
    let defaults = UserDefaults.standard
    
    guard let projectsData = defaults.data(forKey: "projects") else {
        print("❌ Данные проектов не найдены")
        return
    }
    
    do {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let projects = try decoder.decode([ProjectData].self, from: projectsData)
        
        print("✅ Загружено проектов: \(projects.count)")
        
        var workProjects = 0
        var personalProjects = 0
        
        for project in projects {
            let workIndicator = project.isWorkRelated ? "💼" : "🏠"
            let colorInfo = project.color ?? "без цвета"
            print("   - \(project.type.emoji) \(project.name) \(workIndicator) (\(colorInfo))")
            
            if project.isWorkRelated {
                workProjects += 1
            } else {
                personalProjects += 1
            }
        }
        
        print("   📈 Рабочих проектов: \(workProjects)")
        print("   🏠 Личных проектов: \(personalProjects)")
        
        // Проверяем избранное
        if let favoritesData = defaults.data(forKey: "favoriteProjects") {
            let favoriteIds = try decoder.decode([UUID].self, from: favoritesData)
            print("   ⭐ В избранном: \(favoriteIds.count) проектов")
        }
        
    } catch {
        print("❌ Ошибка декодирования: \(error)")
    }
}

// Тестируем создание нового проекта
func testProjectCreation() {
    print("\n🛠️ Тестирование создания нового проекта:")
    
    let newProject = ProjectData(
        id: UUID(),
        name: "Тестовый проект",
        type: .creative,
        isWorkRelated: true,
        color: "#FF5722",
        isActive: true,
        isArchived: false,
        createdAt: Date(),
        lastUsedAt: nil
    )
    
    print("✅ Создан тестовый проект:")
    print("   - Название: \(newProject.name)")
    print("   - Тип: \(newProject.type.emoji) \(newProject.type.rawValue)")
    print("   - Рабочий: \(newProject.isWorkRelated ? "Да" : "Нет")")
    print("   - Цвет: \(newProject.color ?? "без цвета")")
}

// Тестируем цветовые индикаторы
func testColorIndicators() {
    print("\n🎨 Тестирование цветовых индикаторов:")
    
    let testProjects = [
        ("Работа", ProjectTypeData.work, true, "#3B82F6"),
        ("Спорт", ProjectTypeData.health, false, "#EF4444"),
        ("Изучение Swift", ProjectTypeData.learning, false, "#8B5CF6"),
        ("Дизайн", ProjectTypeData.creative, true, "#F59E0B")
    ]
    
    for (name, type, isWork, color) in testProjects {
        let workIndicator = isWork ? "💼" : "🏠"
        print("   \(type.emoji)\(workIndicator) \(name) (\(color))")
    }
}

// Тестируем статистику работы/личного времени
func testWorkPersonalStats() {
    print("\n📈 Тестирование статистики работы/личного времени:")
    
    // Симулируем интервалы
    let workIntervals = 5
    let personalIntervals = 3
    
    print("   💼 Рабочих интервалов: \(workIntervals)")
    print("   🏠 Личных интервалов: \(personalIntervals)")
    print("   📊 Соотношение работа/личное: \(workIntervals):\(personalIntervals)")
    
    let totalIntervals = workIntervals + personalIntervals
    let workPercentage = Double(workIntervals) / Double(totalIntervals) * 100
    let personalPercentage = Double(personalIntervals) / Double(totalIntervals) * 100
    
    print("   📈 Работа: \(String(format: "%.1f", workPercentage))%")
    print("   🏠 Личное: \(String(format: "%.1f", personalPercentage))%")
}

// Проверяем, что приложение запущено
func checkAppStatus() -> Bool {
    let task = Process()
    task.launchPath = "/usr/bin/pgrep"
    task.arguments = ["uProd"]
    
    let pipe = Pipe()
    task.standardOutput = pipe
    task.launch()
    task.waitUntilExit()
    
    let data = pipe.fileHandleForReading.readDataToEndOfFile()
    let output = String(data: data, encoding: .utf8) ?? ""
    
    return !output.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
}

// Запускаем все тесты
if checkAppStatus() {
    print("✅ uProd запущен")
} else {
    print("❌ uProd не запущен")
    exit(1)
}

testProjectData()
testProjectCreation()
testColorIndicators()
testWorkPersonalStats()

print("\n🎯 Что нужно проверить вручную:")
print("1. 📱 Откройте меню в строке меню (иконка ⏱️)")
print("2. 🎨 Проверьте цветовые индикаторы рядом с проектами")
print("3. 🔢 Должны быть горячие клавиши 1, 2, 3 для избранных проектов")
print("4. 📂 Откройте 'Управление проектами'")
print("5. ➕ Попробуйте создать новый проект")
print("6. 🎛️ Проверьте диалог создания проекта:")
print("   - Поле названия")
print("   - Выбор типа проекта")
print("   - Выбор цвета (color picker)")
print("   - Чекбокс 'Рабочий проект'")
print("7. ⏰ Запустите интервал с проектом")
print("8. 📊 Проверьте статистику")

print("\n🎉 Все автоматические тесты пройдены!")
print("💡 Теперь протестируйте интерфейс вручную")
