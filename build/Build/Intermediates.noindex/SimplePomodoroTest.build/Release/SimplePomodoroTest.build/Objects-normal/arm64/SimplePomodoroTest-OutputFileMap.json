{"": {"const-values": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "dependencies": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.d", "diagnostics": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.dia", "emit-module-dependencies": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftdeps"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/AppDelegate.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/DemoDataManager.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/GradientViews.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalCompletionWindow.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalNotificationWindow.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/LaunchAtLoginManager.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/MotivationManager.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/PomodoroTimer.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Project.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectEditDialog.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManagementWindow.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManager.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/SettingsWindow.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsManager.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsWindow.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ViewController.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/WorkPatternAnalyzer.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o"}, "/Users/<USER>/EVO/uProd/SimplePomodoroTest/main.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o"}, "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o"}}