{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "macosx", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "9dd5dde06f08220656a495dd9cbb1605d3f4d4ea653168ccfb4b61058df33fe7", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule"}, "guid": "9dd5dde06f08220656a495dd9cbb16058dbdf862ec9a367ef7a6916b41b73a4c", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"children": [{"fileType": "image.png", "guid": "9dd5dde06f08220656a495dd9cbb16051d41239aa3627333347f433afc6cd8ac", "path": "battery_level_1.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "9dd5dde06f08220656a495dd9cbb16059e8265afbd6575ef155c279de20be4b9", "path": "battery_level_2.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "9dd5dde06f08220656a495dd9cbb16052d34cf3f8b98f628017f4404e8f90c94", "path": "battery_level_3.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "9dd5dde06f08220656a495dd9cbb1605864daf116ea8bf6f46c17815776d6525", "path": "battery_level_4.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "9dd5dde06f08220656a495dd9cbb16059bcac3a4b7b6e3a1a35d170bb27d82a3", "path": "battery_original.png", "sourceTree": "<group>", "type": "file"}], "guid": "9dd5dde06f08220656a495dd9cbb16053dd7ec7a2d0e80357aef2b84ef07e0ab", "name": "BatteryImages", "path": "BatteryImages", "sourceTree": "<group>", "type": "group"}], "guid": "9dd5dde06f08220656a495dd9cbb16051b797a229ae8fbc562ee8c6ee45010e8", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605d5dbd90b7476aef9efe92683cd72d6df", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "9dd5dde06f08220656a495dd9cbb1605a487ba80950f817e5b0451de45b95ea3", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb16055cc14a457e17c44d712ff94f98292f39", "path": "DemoDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605b85b6f0c43d7df7d6e6d5b76344b9da4", "path": "GradientViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605dd2ca4aca5c699bafcb86f8a1510ac16", "path": "IntervalCompletionWindow.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605cf47ad18131a6b6dc63fb66618af4482", "path": "IntervalNotificationWindow.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb16058960224ff2d3dc2d8986c4079e17b831", "path": "LaunchAtLoginManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605666fa9174a3f727a2b2a98faf9457cf6", "path": "main.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "9dd5dde06f08220656a495dd9cbb16056a84475cd05e9d0dc68219b767c3e6d8", "path": "Base.lproj/Main.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "9dd5dde06f08220656a495dd9cbb1605854a0f840daca09cf8dae7493e367205", "name": "Main.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb16057ab1253ac529d05957f8d0d270c562b9", "path": "MotivationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb16053a3498a8f395975b0da108462edd83a9", "path": "PomodoroTimer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160527c946f32a71b486df7b1719eb25a700", "path": "Project.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160586b8c907bcb4c779a657ed4e610422ab", "path": "ProjectEditDialog.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605c120417736293b5e581cd3155dd65724", "path": "ProjectManagementWindow.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605ba559b142714fc0aae23dc86ba7e2f5f", "path": "ProjectManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605f13e256a63a89f9e32dd56ad5a12fee6", "path": "SettingsWindow.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "9dd5dde06f08220656a495dd9cbb160523dc5311fd138fcd6d230218073047fc", "path": "SimplePomodoroTest.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160594c1563e48f7dc2754bc5dbd589f1f8d", "path": "StatisticsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160517543e32600c1c810f8eef84313f6e26", "path": "StatisticsWindow.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605486089add2c68b0f1caa2a097db5033a", "path": "ViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160522f48bbf16c0557ad7b8af8a7a187385", "path": "WorkPatternAnalyzer.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9dd5dde06f08220656a495dd9cbb1605b1ad63b3905469392b510a08582770d9", "name": "SimplePomodoroTest", "path": "SimplePomodoroTest", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160545291504a8fd33d9fa567751276fc1cd", "path": "PomodoroTimerTests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605b74f7a7fd9285a510d91e987a95c1d5f", "path": "ProjectManagerTests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605e5c7bc2a81767b6d8f0b7e9d48933c32", "path": "SimplePomodoroTestTests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb16054606c8d02f637f64aeebcc634f829ab4", "path": "StatisticsManagerProjectTests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605376fa1a6c12cfe3a766c260785ca41b9", "path": "StatisticsManagerTests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb16053d12862f07e5ad2c9e6f4139b55398eb", "path": "WorkPatternAnalyzerTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9dd5dde06f08220656a495dd9cbb16057372bc2b2892acc7e59181469368b19f", "name": "SimplePomodoroTestTests", "path": "SimplePomodoroTestTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb1605e9de780d8fa17093f721924c968972ad", "path": "SimplePomodoroTestUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9dd5dde06f08220656a495dd9cbb160529f33b5a8a9d76c4b34d70ba724424ce", "path": "SimplePomodoroTestUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9dd5dde06f08220656a495dd9cbb160586d3200f8184c0edc7a6391d158cb2e1", "name": "SimplePomodoroTestUITests", "path": "SimplePomodoroTestUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "9dd5dde06f08220656a495dd9cbb1605ad9dc2cf74b0c6cf790dfa9becb41ef4", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "9dd5dde06f08220656a495dd9cbb16054eed1a0e220a611fe9ddffa503c6b4fc", "name": "SimplePomodoroTest", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "9dd5dde06f08220656a495dd9cbb1605", "path": "/Users/<USER>/EVO/uProd/SimplePomodoroTest.xcodeproj", "projectDirectory": "/Users/<USER>/EVO/uProd", "targets": ["TARGET@v11_hash=c80d728b0ac1c218d1729bdf07b37006", "TARGET@v11_hash=10f53ed548d2e84f4d8b3d3ac8b9ed7d", "TARGET@v11_hash=1751dec93a416cec19263cc9d10a598f"]}