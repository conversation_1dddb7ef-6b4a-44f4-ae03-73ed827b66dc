{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release": {"is-mutated": true}, "/Users/<USER>/EVO/uProd/build/Build/Products": {"is-mutated": true}, "/Users/<USER>/EVO/uProd/build/Build/Products/Release": {"is-mutated": true}, "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"is-mutated": true}, "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/_CodeSignature", "/Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<target-SimplePomodoroTest-****************************************************************--begin-scanning>", "<target-SimplePomodoroTest-****************************************************************--end>", "<target-SimplePomodoroTest-****************************************************************--linker-inputs-ready>", "<target-SimplePomodoroTest-****************************************************************--modules-ready>", "<workspace-Release-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/_CodeSignature", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main.storyboardc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Assets.car", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_signature", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Base.lproj/Main.storyboardc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/PkgInfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest Swift Compilation Finished", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-non-framework-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.hmap"], "roots": ["/tmp/SimplePomodoroTest.dst", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex", "/Users/<USER>/EVO/uProd/build/Build/Products"], "outputs": ["<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest-9dd5dde06f08220656a495dd9cbb1605-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Release-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/EVO/uProd/SimplePomodoroTest.xcodeproj", "signature": "6a4d0da14e3d9f8e8d07696c6474d519"}, "P0:::CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release"]}, "P0:::CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products>", "/Users/<USER>/EVO/uProd/build/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Products/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/EVO/uProd/build/Build/Products/Release", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products/Release>", "/Users/<USER>/EVO/uProd/build/Build/Products/Release"]}, "P0:::Gate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM-target-SimplePomodoroTest-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd", "<GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest-9dd5dde06f08220656a495dd9cbb1605-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-ChangePermissions>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-StripSymbols>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-GenerateStubAPI>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ProductPostprocessingTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-CodeSign>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-Validate>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<Touch /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-CopyAside>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--GeneratedFilesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ProductStructureTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-non-framework-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.hmap"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/PkgInfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--RealityAssetsTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--ModuleMapTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--InfoPlistTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SanitizerTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TestTargetTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TestHostTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--DocumentationTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--CustomTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--StubBinaryTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--start>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--HeadermapTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ProductPostprocessingTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main.storyboardc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Assets.car", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_signature", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Base.lproj/Main.storyboardc/", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest Swift Compilation Finished", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--generated-headers>"]}, "P0:::Gate target-SimplePomodoroTest-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--swift-generated-headers>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/AppDelegate.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Base.lproj/Main.storyboard/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/DemoDataManager.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/GradientViews.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalCompletionWindow.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalNotificationWindow.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/LaunchAtLoginManager.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/MotivationManager.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/PomodoroTimer.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Project.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectEditDialog.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManagementWindow.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManager.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_1.png/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_2.png/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_3.png/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_4.png/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_original.png/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/SettingsWindow.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsManager.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsWindow.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ViewController.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/WorkPatternAnalyzer.swift/", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/main.swift/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent/", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist/", "<target-SimplePomodoroTest-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd normal>", "<TRIGGER: MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/_CodeSignature", "<CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<TRIGGER: CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CompileAssetCatalogVariant thinned /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets/", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "--compile", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.5", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "deps": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "b44b4a649843d0ea058cfc25e7ef3dd8"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets/", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "--compile", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.5", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "deps": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "58e0f93d4d5d4b2a306d13ffd55d6c5a"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CompileStoryboard /Users/<USER>/EVO/uProd/SimplePomodoroTest/Base.lproj/Main.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/EVO/uProd/SimplePomodoroTest/Base.lproj/Main.storyboard", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Base.lproj/Main.storyboard", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main.storyboardc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "uProd", "--output-partial-info-plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "mac", "--minimum-deployment-target", "15.5", "--output-format", "human-readable-text", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Base.lproj/Main.storyboard", "--compilation-directory", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "f460cd80e6effb42b61a14db9d5754ed"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_1.png": {"tool": "shell", "description": "CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_1.png", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_1.png", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/copypng", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_1.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png"], "env": {"DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "TOOLCHAINS": ""}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "43f650f76886646e4d05ac283e378bb9"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_2.png": {"tool": "shell", "description": "CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_2.png", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_2.png", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/copypng", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_2.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png"], "env": {"DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "TOOLCHAINS": ""}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "b57d5f9b544d756398a3d142b459c1b0"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_3.png": {"tool": "shell", "description": "CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_3.png", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_3.png", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/copypng", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_3.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png"], "env": {"DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "TOOLCHAINS": ""}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "af8ac823a4f2c62c3fceb17c92172b55"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_4.png": {"tool": "shell", "description": "CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_4.png", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_4.png", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/copypng", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_level_4.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png"], "env": {"DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "TOOLCHAINS": ""}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "8a269f2604bb43972b538218c633f20b"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_original.png": {"tool": "shell", "description": "CopyPNGFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png /Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_original.png", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_original.png", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/copypng", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Resources/BatteryImages/battery_original.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png"], "env": {"DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "TOOLCHAINS": ""}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "3cb853c4a90f3761edcb29887e059a16"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "deps": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalCompletionWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/LaunchAtLoginManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManagementWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectEditDialog.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/GradientViews.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/WorkPatternAnalyzer.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Project.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/SettingsWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/DemoDataManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/PomodoroTimer.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ViewController.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/main.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalNotificationWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/MotivationManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/AppDelegate.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "uProd", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "15.5", "--bundle-identifier", "com.local.uProd", "--output", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "--target-triple", "arm64-apple-macos15.5", "--binary-file", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "--dependency-file", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "--metadata-file-list", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "signature": "36e7c6f4017faa984ef7fefff860430a"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SimplePomodoroTest.dst>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SimplePomodoroTest.dst>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--begin-linking>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SimplePomodoroTest.dst>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--begin-scanning>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--end": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--entry>", "<CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main.storyboardc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_1.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_2.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_3.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_level_4.png", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/battery_original.png", "<CopySwiftStdlib /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<ExtractAppIntentsMetadata /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd>", "<GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Assets.car", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_signature", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Base.lproj/Main.storyboardc/", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources>", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/PkgInfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<LSRegisterURL /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest Swift Compilation Finished", "<Touch /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-non-framework-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.hmap", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/", "<target-SimplePomodoroTest-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SimplePomodoroTest-****************************************************************--Barrier-ChangePermissions>", "<target-SimplePomodoroTest-****************************************************************--Barrier-CodeSign>", "<target-SimplePomodoroTest-****************************************************************--Barrier-CopyAside>", "<target-SimplePomodoroTest-****************************************************************--Barrier-GenerateStubAPI>", "<target-SimplePomodoroTest-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-SimplePomodoroTest-****************************************************************--Barrier-RegisterProduct>", "<target-SimplePomodoroTest-****************************************************************--Barrier-StripSymbols>", "<target-SimplePomodoroTest-****************************************************************--Barrier-Validate>", "<target-SimplePomodoroTest-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--CustomTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--DocumentationTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--GeneratedFilesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--HeadermapTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--InfoPlistTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--ModuleMapTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--ProductPostprocessingTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--ProductStructureTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--RealityAssetsTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SanitizerTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--StubBinaryTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TestHostTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--TestTargetTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SimplePomodoroTest-****************************************************************--generated-headers>", "<target-SimplePomodoroTest-****************************************************************--swift-generated-headers>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--end>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SimplePomodoroTest.dst>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--entry>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SimplePomodoroTest.dst>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Products/Release>", "<CreateBuildDirectory-/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--linker-inputs-ready>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--modules-ready>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<ExtractAppIntentsMetadata /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Assets.car", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_signature", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest Swift Compilation Finished", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList", "<target-SimplePomodoroTest-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--unsigned-product-ready>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Gate target-SimplePomodoroTest-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-SimplePomodoroTest-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-SimplePomodoroTest-****************************************************************--will-sign>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets/", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "--compile", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.5", "--platform", "macosx", "--bundle-identifier", "com.local.uProd", "--generate-swift-asset-symbols", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "3440ff53f9a0712d9bc5533a94008bba"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist", "<Linked Binary /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd", "<GenerateDSYMFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM/Contents/Resources/DWARF/uProd>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "-o", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "signature": "a11fc1f293f01cf153cd16e009043026"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:LinkAssetCatalog /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/Assets.xcassets/", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources>", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned/", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_signature", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_dependencies"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:LinkStoryboards": {"tool": "shell", "description": "LinkStoryboards", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main.storyboardc", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources/Base.lproj/Main.storyboardc/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "uProd", "--target-device", "mac", "--minimum-deployment-target", "15.5", "--output-format", "human-readable-text", "--link", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main.storyboardc"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/EVO/uProd", "control-enabled": false, "signature": "1446f6949a43780de4c9759190f372b6"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/thinned>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned", "inputs": ["<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_output/unthinned>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["<target-SimplePomodoroTest-****************************************************************--start>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<TRIGGER: MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents", "inputs": ["<target-SimplePomodoroTest-****************************************************************--start>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS", "inputs": ["<target-SimplePomodoroTest-****************************************************************--start>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "inputs": ["<target-SimplePomodoroTest-****************************************************************--start>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources", "<MkDir /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Resources>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/assetcatalog_generated_info.plist", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/PkgInfo"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:ProcessProductPackaging /Users/<USER>/EVO/uProd/SimplePomodoroTest/SimplePomodoroTest.entitlements /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/EVO/uProd/SimplePomodoroTest/SimplePomodoroTest.entitlements /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/SimplePomodoroTest.entitlements", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist", "<target-SimplePomodoroTest-****************************************************************--ProductStructureTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "<target-SimplePomodoroTest-****************************************************************--ProductStructureTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent", "-o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "signature": "ab7f6a078eb1e0f76cd69648e0bb5661"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "<target-SimplePomodoroTest-****************************************************************--Barrier-CodeSign>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:RegisterWithLaunchServices /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["<target-SimplePomodoroTest-****************************************************************--Barrier-Validate>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:SwiftDriver Compilation SimplePomodoroTest normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation SimplePomodoroTest normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalCompletionWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/LaunchAtLoginManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManagementWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectEditDialog.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/GradientViews.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/WorkPatternAnalyzer.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Project.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/SettingsWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/DemoDataManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/PomodoroTimer.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ViewController.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/main.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalNotificationWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/MotivationManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/AppDelegate.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap", "<ClangStatCache /Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest Swift Compilation Finished"]}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Touch /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "shell", "description": "Touch /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "<target-SimplePomodoroTest-****************************************************************--Barrier-Validate>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "signature": "41c722511563519ea88e45c571795aca"}, "P0:target-SimplePomodoroTest-****************************************************************-:Release:Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/Info.plist", "<target-SimplePomodoroTest-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-SimplePomodoroTest-****************************************************************--will-sign>", "<target-SimplePomodoroTest-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"], "outputs": ["<Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>", "<TRIGGER: Validate /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest-9dd5dde06f08220656a495dd9cbb1605-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest-9dd5dde06f08220656a495dd9cbb1605-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest-9dd5dde06f08220656a495dd9cbb1605-VFS/all-product-headers.yaml"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo/", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json/", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc/", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule/", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:Ld /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd normal": {"tool": "shell", "description": "Ld /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd normal", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "/Users/<USER>/EVO/uProd/build/Build/Products/Release", "<target-SimplePomodoroTest-****************************************************************--generated-headers>", "<target-SimplePomodoroTest-****************************************************************--swift-generated-headers>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd", "<Linked Binary /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos15.5", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-L/Users/<USER>/EVO/uProd/build/Build/Products/Release", "-F/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/EagerLinkingTBDs/Release", "-F/Users/<USER>/EVO/uProd/build/Build/Products/Release", "-filelist", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_lto.o", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat", "-fobjc-link-runtime", "-fprofile-instr-generate", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd"], "env": {}, "working-directory": "/Users/<USER>/EVO/uProd", "deps": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd_dependency_info.dat"], "deps-style": "dependency-info", "signature": "c91af59f3c97442d92e5ceee14888de7"}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:SwiftDriver Compilation Requirements SimplePomodoroTest normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements SimplePomodoroTest normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalCompletionWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/LaunchAtLoginManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManagementWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectEditDialog.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/GradientViews.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/WorkPatternAnalyzer.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ProjectManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/StatisticsWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/Project.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/SettingsWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/DemoDataManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/PomodoroTimer.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/ViewController.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/main.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/IntervalNotificationWindow.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/MotivationManager.swift", "/Users/<USER>/EVO/uProd/SimplePomodoroTest/AppDelegate.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap", "<ClangStatCache /Users/<USER>/EVO/uProd/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-SimplePomodoroTest-****************************************************************--copy-headers-completion>", "<target-SimplePomodoroTest-****************************************************************--ModuleVerifierTaskProducer>", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalCompletionWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/LaunchAtLoginManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManagementWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectEditDialog.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GradientViews.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/WorkPatternAnalyzer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ProjectManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/StatisticsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/Project.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SettingsWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/DemoDataManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/PomodoroTimer.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/main.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/IntervalNotificationWindow.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/MotivationManager.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-master.swiftconstvalues", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftmodule", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftsourceinfo", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.abi.json", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.swiftdoc"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "inputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd-Swift.h", "<target-SimplePomodoroTest-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/uProd-Swift.h"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/DerivedSources/Entitlements.plist"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest-OutputFileMap.json"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/SimplePomodoroTest_const_extract_protocols.json"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.LinkFileList"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftConstValuesFileList"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/Objects-normal/arm64/uProd.SwiftFileList"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/empty-uProd.plist"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-non-framework-target-headers.hmap", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-non-framework-target-headers.hmap"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-all-target-headers.hmap"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-generated-files.hmap"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-own-target-headers.hmap"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd-project-headers.hmap"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyMetadataFileList"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.DependencyStaticMetadataFileList"]}, "P2:target-SimplePomodoroTest-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.hmap", "inputs": ["<target-SimplePomodoroTest-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/SimplePomodoroTest.build/Release/SimplePomodoroTest.build/uProd.hmap"]}}}