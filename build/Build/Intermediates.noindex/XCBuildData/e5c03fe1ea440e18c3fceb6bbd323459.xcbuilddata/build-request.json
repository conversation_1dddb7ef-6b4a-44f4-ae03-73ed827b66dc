{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "9dd5dde06f08220656a495dd9cbb1605f5a2038067c0228e2110e9ae50a5ef4d"}], "containerPath": "/Users/<USER>/EVO/uProd/SimplePomodoroTest.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.5", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/EVO/uProd/build/Build/Products", "derivedDataPath": "/Users/<USER>/EVO/uProd/build", "indexDataStoreFolderPath": "/Users/<USER>/EVO/uProd/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/EVO/uProd/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/EVO/uProd/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "CLANG_COVERAGE_MAPPING": "YES", "CLANG_PROFILE_DATA_DIRECTORY": "/Users/<USER>/EVO/uProd/build/Build/ProfileData", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "80", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}