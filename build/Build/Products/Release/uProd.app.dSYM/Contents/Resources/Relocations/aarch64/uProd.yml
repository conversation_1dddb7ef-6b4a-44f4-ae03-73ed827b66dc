---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/EVO/uProd/build/Build/Products/Release/uProd.app/Contents/MacOS/uProd'
relocations:
  - { offset: 0x11FB5F, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_timestamp, symObjAddr: 0x0, symBinAddr: 0x1000590B8, symSize: 0x28 }
  - { offset: 0x11FB6D, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_timestamp, symObjAddr: 0x0, symBinAddr: 0x1000590B8, symSize: 0x28 }
  - { offset: 0x11FB7F, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_magic, symObjAddr: 0x28, symBinAddr: 0x1000590E0, symSize: 0x14 }
  - { offset: 0x11FB91, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_dumped, symObjAddr: 0x3C, symBinAddr: 0x1000590F4, symSize: 0x8 }
  - { offset: 0x11FBBA, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_padding_bytes, symObjAddr: 0x44, symBinAddr: 0x1000590FC, symSize: 0xC }
  - { offset: 0x11FBCC, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_version, symObjAddr: 0x50, symBinAddr: 0x100059108, symSize: 0xC }
  - { offset: 0x11FBE5, size: 0x8, addend: 0x0, symName: ___llvm_profile_reset_counters, symObjAddr: 0x5C, symBinAddr: 0x100059114, symSize: 0xD4 }
  - { offset: 0x11FCDE, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x1000591E8, symSize: 0xC }
  - { offset: 0x11FCEC, size: 0x8, addend: 0x0, symName: _lprofProfileDumped, symObjAddr: 0x0, symBinAddr: 0x1000591E8, symSize: 0xC }
  - { offset: 0x11FCFE, size: 0x8, addend: 0x0, symName: _lprofSetProfileDumped, symObjAddr: 0xC, symBinAddr: 0x1000591F4, symSize: 0xC }
  - { offset: 0x11FD3A, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x100059200, symSize: 0xC }
  - { offset: 0x11FD48, size: 0x8, addend: 0x0, symName: _getValueProfRecordHeaderSize, symObjAddr: 0x0, symBinAddr: 0x100059200, symSize: 0xC }
  - { offset: 0x11FD61, size: 0x8, addend: 0x0, symName: _getValueProfRecordSize, symObjAddr: 0xC, symBinAddr: 0x10005920C, symSize: 0x10 }
  - { offset: 0x11FD77, size: 0x8, addend: 0x0, symName: _getValueProfRecordSize, symObjAddr: 0xC, symBinAddr: 0x10005920C, symSize: 0x10 }
  - { offset: 0x11FD8A, size: 0x8, addend: 0x0, symName: _getValueProfRecordValueData, symObjAddr: 0x1C, symBinAddr: 0x10005921C, symSize: 0x14 }
  - { offset: 0x11FDB3, size: 0x8, addend: 0x0, symName: _getValueProfRecordNumValueData, symObjAddr: 0x30, symBinAddr: 0x100059230, symSize: 0x1BC }
  - { offset: 0x11FDD3, size: 0x8, addend: 0x0, symName: _getValueProfRecordNext, symObjAddr: 0x1EC, symBinAddr: 0x1000593EC, symSize: 0x1D8 }
  - { offset: 0x11FE11, size: 0x8, addend: 0x0, symName: _getFirstValueProfRecord, symObjAddr: 0x3C4, symBinAddr: 0x1000595C4, symSize: 0x8 }
  - { offset: 0x11FE23, size: 0x8, addend: 0x0, symName: _getValueProfDataSize, symObjAddr: 0x3CC, symBinAddr: 0x1000595CC, symSize: 0xF0 }
  - { offset: 0x11FEAE, size: 0x8, addend: 0x0, symName: _serializeValueProfRecordFrom, symObjAddr: 0x4BC, symBinAddr: 0x1000596BC, symSize: 0xA0 }
  - { offset: 0x11FF22, size: 0x8, addend: 0x0, symName: _serializeValueProfDataFrom, symObjAddr: 0x55C, symBinAddr: 0x10005975C, symSize: 0x634 }
  - { offset: 0x1200B5, size: 0x8, addend: 0x0, symName: _InstProfClzll, symObjAddr: 0xB90, symBinAddr: 0x100059D90, symSize: 0x8 }
  - { offset: 0x1200C7, size: 0x8, addend: 0x0, symName: _InstProfPopcountll, symObjAddr: 0xB98, symBinAddr: 0x100059D98, symSize: 0x14 }
  - { offset: 0x1200E0, size: 0x8, addend: 0x0, symName: _InstrProfGetRangeRepValue, symObjAddr: 0xBAC, symBinAddr: 0x100059DAC, symSize: 0x38 }
  - { offset: 0x120109, size: 0x8, addend: 0x0, symName: _InstrProfIsSingleValRange, symObjAddr: 0xBE4, symBinAddr: 0x100059DE4, symSize: 0x20 }
  - { offset: 0x12011B, size: 0x8, addend: 0x0, symName: _lprofSetupValueProfiler, symObjAddr: 0xC04, symBinAddr: 0x100059E04, symSize: 0x68 }
  - { offset: 0x12015A, size: 0x8, addend: 0x0, symName: _lprofSetMaxValsPerSite, symObjAddr: 0xC6C, symBinAddr: 0x100059E6C, symSize: 0x18 }
  - { offset: 0x12016C, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_num_value_sites, symObjAddr: 0xC84, symBinAddr: 0x100059E84, symSize: 0xC }
  - { offset: 0x12017E, size: 0x8, addend: 0x0, symName: ___llvm_profile_iterate_data, symObjAddr: 0xC90, symBinAddr: 0x100059E90, symSize: 0x8 }
  - { offset: 0x120190, size: 0x8, addend: 0x0, symName: ___llvm_get_function_addr, symObjAddr: 0xC98, symBinAddr: 0x100059E98, symSize: 0x8 }
  - { offset: 0x1201B7, size: 0x8, addend: 0x0, symName: ___llvm_profile_instrument_target, symObjAddr: 0xCA0, symBinAddr: 0x100059EA0, symSize: 0x288 }
  - { offset: 0x120251, size: 0x8, addend: 0x0, symName: ___llvm_profile_instrument_target_value, symObjAddr: 0xF28, symBinAddr: 0x10005A128, symSize: 0x2C8 }
  - { offset: 0x1202DA, size: 0x8, addend: 0x0, symName: ___llvm_profile_instrument_memop, symObjAddr: 0x11F0, symBinAddr: 0x10005A3F0, symSize: 0x38 }
  - { offset: 0x120328, size: 0x8, addend: 0x0, symName: _lprofGetVPDataReader, symObjAddr: 0x1228, symBinAddr: 0x10005A428, symSize: 0xC }
  - { offset: 0x12033A, size: 0x8, addend: 0x0, symName: _initializeValueProfRuntimeRecord, symObjAddr: 0x1234, symBinAddr: 0x10005A434, symSize: 0x210 }
  - { offset: 0x120353, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0x1444, symBinAddr: 0x10005A644, symSize: 0x14 }
  - { offset: 0x120369, size: 0x8, addend: 0x0, symName: _getNumValueDataForSiteWrapper, symObjAddr: 0x1444, symBinAddr: 0x10005A644, symSize: 0x14 }
  - { offset: 0x12037C, size: 0x8, addend: 0x0, symName: _getValueProfDataSizeWrapper, symObjAddr: 0x1458, symBinAddr: 0x10005A658, symSize: 0xE0 }
  - { offset: 0x12041D, size: 0x8, addend: 0x0, symName: _getNumValueSitesRT, symObjAddr: 0x1574, symBinAddr: 0x10005A774, symSize: 0x10 }
  - { offset: 0x12042F, size: 0x8, addend: 0x0, symName: _getNumValueDataRT, symObjAddr: 0x1584, symBinAddr: 0x10005A784, symSize: 0x1C4 }
  - { offset: 0x120441, size: 0x8, addend: 0x0, symName: _getNextNValueData, symObjAddr: 0x1538, symBinAddr: 0x10005A738, symSize: 0x3C }
  - { offset: 0x12047D, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x10005A948, symSize: 0x1C }
  - { offset: 0x12048B, size: 0x8, addend: 0x0, symName: ___llvm_profile_is_continuous_mode_enabled, symObjAddr: 0x0, symBinAddr: 0x10005A948, symSize: 0x1C }
  - { offset: 0x12049D, size: 0x8, addend: 0x0, symName: ___llvm_profile_enable_continuous_mode, symObjAddr: 0x1C, symBinAddr: 0x10005A964, symSize: 0x10 }
  - { offset: 0x1204AF, size: 0x8, addend: 0x0, symName: ___llvm_profile_disable_continuous_mode, symObjAddr: 0x2C, symBinAddr: 0x10005A974, symSize: 0xC }
  - { offset: 0x1204C1, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_page_size, symObjAddr: 0x38, symBinAddr: 0x10005A980, symSize: 0xC }
  - { offset: 0x1204D3, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer, symObjAddr: 0x44, symBinAddr: 0x10005A98C, symSize: 0xC8 }
  - { offset: 0x1205D0, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_size_for_buffer_internal, symObjAddr: 0x10C, symBinAddr: 0x10005AA54, symSize: 0x1C8 }
  - { offset: 0x120837, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_data, symObjAddr: 0x2D4, symBinAddr: 0x10005AC1C, symSize: 0x10 }
  - { offset: 0x120849, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x2E4, symBinAddr: 0x10005AC2C, symSize: 0x10 }
  - { offset: 0x12085F, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_data_size, symObjAddr: 0x2E4, symBinAddr: 0x10005AC2C, symSize: 0x10 }
  - { offset: 0x120871, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_vtable, symObjAddr: 0x2F4, symBinAddr: 0x10005AC3C, symSize: 0x18 }
  - { offset: 0x120883, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_vtable_section_size, symObjAddr: 0x30C, symBinAddr: 0x10005AC54, symSize: 0x8 }
  - { offset: 0x120895, size: 0x8, addend: 0x0, symName: ___llvm_profile_counter_entry_size, symObjAddr: 0x314, symBinAddr: 0x10005AC5C, symSize: 0x20 }
  - { offset: 0x1208B6, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_num_counters, symObjAddr: 0x334, symBinAddr: 0x10005AC7C, symSize: 0x4C }
  - { offset: 0x12090F, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_counters_size, symObjAddr: 0x380, symBinAddr: 0x10005ACC8, symSize: 0x5C }
  - { offset: 0x1209A1, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_padding_sizes_for_counters, symObjAddr: 0x3EC, symBinAddr: 0x10005AD24, symSize: 0x160 }
  - { offset: 0x120A79, size: 0x8, addend: 0x0, symName: _initBufferWriter, symObjAddr: 0x54C, symBinAddr: 0x10005AE84, symSize: 0x10 }
  - { offset: 0x120A92, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_buffer, symObjAddr: 0x55C, symBinAddr: 0x10005AE94, symSize: 0x34 }
  - { offset: 0x120AD0, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_buffer_internal, symObjAddr: 0x590, symBinAddr: 0x10005AEC8, symSize: 0x44 }
  - { offset: 0x120B0F, size: 0x8, addend: 0x0, symName: _doneCopyingAllEltsFromSrcBufferAtIdx, symObjAddr: 0x5D4, symBinAddr: 0x10005AF0C, symSize: 0x1C }
  - { offset: 0x120B21, size: 0x8, addend: 0x0, symName: _isIncrementalProfileWriterDone, symObjAddr: 0x5F0, symBinAddr: 0x10005AF28, symSize: 0x10 }
  - { offset: 0x120B41, size: 0x8, addend: 0x0, symName: _tryWriteOneChunkAndUpdateWriterState, symObjAddr: 0x600, symBinAddr: 0x10005AF38, symSize: 0x110 }
  - { offset: 0x120B87, size: 0x8, addend: 0x0, symName: _writeProfileDataInChunks, symObjAddr: 0x710, symBinAddr: 0x10005B048, symSize: 0x158 }
  - { offset: 0x120BDC, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_buffer_incremental, symObjAddr: 0x868, symBinAddr: 0x10005B1A0, symSize: 0x1C8 }
  - { offset: 0x120C57, size: 0x8, addend: 0x0, symName: ___llvm_profile_init_incremental_writer_state, symObjAddr: 0xA30, symBinAddr: 0x10005B368, symSize: 0x68 }
  - { offset: 0x120C8F, size: 0x8, addend: 0x0, symName: ___llvm_profile_incremental_writer_done, symObjAddr: 0xA98, symBinAddr: 0x10005B3D0, symSize: 0x74 }
  - { offset: 0x120CDA, size: 0x8, addend: 0x0, symName: _lprofCreateBufferIOInternal, symObjAddr: 0x0, symBinAddr: 0x10005B444, symSize: 0x78 }
  - { offset: 0x120CEF, size: 0x8, addend: 0x0, symName: _lprofCreateBufferIOInternal, symObjAddr: 0x0, symBinAddr: 0x10005B444, symSize: 0x78 }
  - { offset: 0x120D52, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_path_prefix, symObjAddr: 0x78, symBinAddr: 0x10005B4BC, symSize: 0x154 }
  - { offset: 0x120DAB, size: 0x8, addend: 0x0, symName: _getCurFilenameLength, symObjAddr: 0x1CC, symBinAddr: 0x10005B610, symSize: 0x338 }
  - { offset: 0x120DF6, size: 0x8, addend: 0x0, symName: _getCurFilename, symObjAddr: 0x504, symBinAddr: 0x10005B948, symSize: 0x338 }
  - { offset: 0x120EFB, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_filename, symObjAddr: 0x83C, symBinAddr: 0x10005BC80, symSize: 0x8C }
  - { offset: 0x120F4D, size: 0x8, addend: 0x0, symName: ___llvm_profile_initialize_file, symObjAddr: 0x8C8, symBinAddr: 0x10005BD0C, symSize: 0x7C }
  - { offset: 0x120FB6, size: 0x8, addend: 0x0, symName: _parseAndSetFilename, symObjAddr: 0x944, symBinAddr: 0x10005BD88, symSize: 0x580 }
  - { offset: 0x121226, size: 0x8, addend: 0x0, symName: _truncateCurrentFile, symObjAddr: 0x1E70, symBinAddr: 0x10005D2B4, symSize: 0xD8 }
  - { offset: 0x12129B, size: 0x8, addend: 0x0, symName: _initializeProfileForContinuousMode, symObjAddr: 0x1F48, symBinAddr: 0x10005D38C, symSize: 0x25C }
  - { offset: 0x1214B4, size: 0x8, addend: 0x0, symName: ___llvm_profile_initialize, symObjAddr: 0xEC4, symBinAddr: 0x10005C308, symSize: 0xBC }
  - { offset: 0x121572, size: 0x8, addend: 0x0, symName: ___llvm_profile_register_write_file_atexit, symObjAddr: 0xF80, symBinAddr: 0x10005C3C4, symSize: 0x48 }
  - { offset: 0x121593, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_filename, symObjAddr: 0xFC8, symBinAddr: 0x10005C40C, symSize: 0x3C }
  - { offset: 0x1215C2, size: 0x8, addend: 0x0, symName: ___llvm_profile_write_file, symObjAddr: 0x1004, symBinAddr: 0x10005C448, symSize: 0x1BC }
  - { offset: 0x12169B, size: 0x8, addend: 0x0, symName: _writeFile, symObjAddr: 0x11C0, symBinAddr: 0x10005C604, symSize: 0x29C }
  - { offset: 0x12194D, size: 0x8, addend: 0x0, symName: _createProfileDir, symObjAddr: 0x21A4, symBinAddr: 0x10005D5E8, symSize: 0x98 }
  - { offset: 0x121992, size: 0x8, addend: 0x0, symName: _getProfileFileSizeForMerging, symObjAddr: 0x1900, symBinAddr: 0x10005CD44, symSize: 0x114 }
  - { offset: 0x121A23, size: 0x8, addend: 0x0, symName: _mmapProfileForMerging, symObjAddr: 0x1A14, symBinAddr: 0x10005CE58, symSize: 0xC0 }
  - { offset: 0x121ADB, size: 0x8, addend: 0x0, symName: ___llvm_profile_dump, symObjAddr: 0x145C, symBinAddr: 0x10005C8A0, symSize: 0x70 }
  - { offset: 0x121B44, size: 0x8, addend: 0x0, symName: ___llvm_orderfile_write_file, symObjAddr: 0x14CC, symBinAddr: 0x10005C910, symSize: 0x244 }
  - { offset: 0x121C88, size: 0x8, addend: 0x0, symName: ___llvm_orderfile_dump, symObjAddr: 0x1710, symBinAddr: 0x10005CB54, symSize: 0x4 }
  - { offset: 0x121C9E, size: 0x8, addend: 0x0, symName: ___llvm_orderfile_dump, symObjAddr: 0x1710, symBinAddr: 0x10005CB54, symSize: 0x4 }
  - { offset: 0x121CA9, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1714, symBinAddr: 0x10005CB58, symSize: 0x4 }
  - { offset: 0x121CBF, size: 0x8, addend: 0x0, symName: _writeFileWithoutReturn, symObjAddr: 0x1714, symBinAddr: 0x10005CB58, symSize: 0x4 }
  - { offset: 0x121CD8, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_file_object, symObjAddr: 0x1718, symBinAddr: 0x10005CB5C, symSize: 0x1E8 }
  - { offset: 0x121E5B, size: 0x8, addend: 0x0, symName: _mmapForContinuousMode, symObjAddr: 0x1AD4, symBinAddr: 0x10005CF18, symSize: 0x260 }
  - { offset: 0x122010, size: 0x8, addend: 0x0, symName: _fileWriter, symObjAddr: 0x1D34, symBinAddr: 0x10005D178, symSize: 0x13C }
  - { offset: 0x12206C, size: 0x8, addend: 0x0, symName: _exitSignalHandler, symObjAddr: 0x223C, symBinAddr: 0x10005D680, symSize: 0x10 }
  - { offset: 0x1220BF, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x10005D690, symSize: 0xD4 }
  - { offset: 0x1220CD, size: 0x8, addend: 0x0, symName: _lprofGetLoadModuleSignature, symObjAddr: 0x0, symBinAddr: 0x10005D690, symSize: 0xD4 }
  - { offset: 0x1221CC, size: 0x8, addend: 0x0, symName: ___llvm_profile_check_compatibility, symObjAddr: 0xD4, symBinAddr: 0x10005D764, symSize: 0x1D8 }
  - { offset: 0x1222F1, size: 0x8, addend: 0x0, symName: ___llvm_profile_merge_from_buffer, symObjAddr: 0x2AC, symBinAddr: 0x10005D93C, symSize: 0x4B4 }
  - { offset: 0x122431, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x10005DDF0, symSize: 0xF4 }
  - { offset: 0x12243F, size: 0x8, addend: 0x0, symName: _lprofMergeValueProfData, symObjAddr: 0x0, symBinAddr: 0x10005DDF0, symSize: 0xF4 }
  - { offset: 0x1224D7, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x10005DEE4, symSize: 0x70 }
  - { offset: 0x1224E5, size: 0x8, addend: 0x0, symName: _lprofBufferWriter, symObjAddr: 0x0, symBinAddr: 0x10005DEE4, symSize: 0x70 }
  - { offset: 0x1224FE, size: 0x8, addend: 0x0, symName: _lprofCreateBufferIO, symObjAddr: 0x70, symBinAddr: 0x10005DF54, symSize: 0x48 }
  - { offset: 0x12251E, size: 0x8, addend: 0x0, symName: _lprofDeleteBufferIO, symObjAddr: 0xB8, symBinAddr: 0x10005DF9C, symSize: 0x50 }
  - { offset: 0x122557, size: 0x8, addend: 0x0, symName: _lprofBufferIOWrite, symObjAddr: 0x108, symBinAddr: 0x10005DFEC, symSize: 0x13C }
  - { offset: 0x1225C2, size: 0x8, addend: 0x0, symName: _lprofBufferIOFlush, symObjAddr: 0x244, symBinAddr: 0x10005E128, symSize: 0xC4 }
  - { offset: 0x1225E1, size: 0x8, addend: 0x0, symName: _lprofWriteData, symObjAddr: 0x308, symBinAddr: 0x10005E1EC, symSize: 0xD8 }
  - { offset: 0x1226DE, size: 0x8, addend: 0x0, symName: _lprofWriteDataImpl, symObjAddr: 0x3E0, symBinAddr: 0x10005E2C4, symSize: 0x320 }
  - { offset: 0x1227E7, size: 0x8, addend: 0x0, symName: _createHeader, symObjAddr: 0x700, symBinAddr: 0x10005E5E4, symSize: 0x1A4 }
  - { offset: 0x1228C9, size: 0x8, addend: 0x0, symName: _writeValueProfData, symObjAddr: 0x8A4, symBinAddr: 0x10005E788, symSize: 0x15C }
  - { offset: 0x122983, size: 0x8, addend: 0x0, symName: _writeOneValueProfData, symObjAddr: 0x105C, symBinAddr: 0x10005EF40, symSize: 0x4AC }
  - { offset: 0x122AE2, size: 0x8, addend: 0x0, symName: _lprofWriteOneBinaryId, symObjAddr: 0xA00, symBinAddr: 0x10005E8E4, symSize: 0x90 }
  - { offset: 0x122B01, size: 0x8, addend: 0x0, symName: _addProfDataIOVecToWriterState, symObjAddr: 0xA90, symBinAddr: 0x10005E974, symSize: 0xB4 }
  - { offset: 0x122B31, size: 0x8, addend: 0x0, symName: _addPaddingBytesToWriterState, symObjAddr: 0xB44, symBinAddr: 0x10005EA28, symSize: 0x84 }
  - { offset: 0x122B60, size: 0x8, addend: 0x0, symName: _addDataBytesToWriterState, symObjAddr: 0xBC8, symBinAddr: 0x10005EAAC, symSize: 0xB0 }
  - { offset: 0x122B9D, size: 0x8, addend: 0x0, symName: _initIncrementalProfileWriterState, symObjAddr: 0xC78, symBinAddr: 0x10005EB5C, symSize: 0x3E4 }
  - { offset: 0x122DE7, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x10005F3EC, symSize: 0xC }
  - { offset: 0x122DF5, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_data, symObjAddr: 0x0, symBinAddr: 0x10005F3EC, symSize: 0xC }
  - { offset: 0x122E07, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_data, symObjAddr: 0xC, symBinAddr: 0x10005F3F8, symSize: 0xC }
  - { offset: 0x122E19, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_names, symObjAddr: 0x18, symBinAddr: 0x10005F404, symSize: 0xC }
  - { offset: 0x122E2B, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_names, symObjAddr: 0x24, symBinAddr: 0x10005F410, symSize: 0xC }
  - { offset: 0x122E3D, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_counters, symObjAddr: 0x30, symBinAddr: 0x10005F41C, symSize: 0xC }
  - { offset: 0x122E4F, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_counters, symObjAddr: 0x3C, symBinAddr: 0x10005F428, symSize: 0xC }
  - { offset: 0x122E61, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_bitmap, symObjAddr: 0x48, symBinAddr: 0x10005F434, symSize: 0xC }
  - { offset: 0x122E73, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_bitmap, symObjAddr: 0x54, symBinAddr: 0x10005F440, symSize: 0xC }
  - { offset: 0x122E85, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_vtables, symObjAddr: 0x60, symBinAddr: 0x10005F44C, symSize: 0xC }
  - { offset: 0x122E97, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_vtables, symObjAddr: 0x6C, symBinAddr: 0x10005F458, symSize: 0xC }
  - { offset: 0x122EA9, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_vtabnames, symObjAddr: 0x78, symBinAddr: 0x10005F464, symSize: 0xC }
  - { offset: 0x122EBB, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_vtabnames, symObjAddr: 0x84, symBinAddr: 0x10005F470, symSize: 0xC }
  - { offset: 0x122ECD, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_orderfile, symObjAddr: 0x90, symBinAddr: 0x10005F47C, symSize: 0xC }
  - { offset: 0x122EDF, size: 0x8, addend: 0x0, symName: ___llvm_profile_begin_vnodes, symObjAddr: 0x9C, symBinAddr: 0x10005F488, symSize: 0xC }
  - { offset: 0x122EF1, size: 0x8, addend: 0x0, symName: ___llvm_profile_end_vnodes, symObjAddr: 0xA8, symBinAddr: 0x10005F494, symSize: 0xC }
  - { offset: 0x122F03, size: 0x8, addend: 0x0, symName: ___llvm_write_binary_ids, symObjAddr: 0xB4, symBinAddr: 0x10005F4A0, symSize: 0x8 }
  - { offset: 0x122F3F, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_InstrProfilingRuntime.cpp, symObjAddr: 0x0, symBinAddr: 0x10005F4A8, symSize: 0x1C }
  - { offset: 0x122F5B, size: 0x8, addend: 0x0, symName: __GLOBAL__sub_I_InstrProfilingRuntime.cpp, symObjAddr: 0x0, symBinAddr: 0x10005F4A8, symSize: 0x1C }
  - { offset: 0x122FD4, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x10005F4C4, symSize: 0x5C }
  - { offset: 0x122FE9, size: 0x8, addend: 0x0, symName: ___llvm_profile_recursive_mkdir, symObjAddr: 0x0, symBinAddr: 0x10005F4C4, symSize: 0x5C }
  - { offset: 0x123028, size: 0x8, addend: 0x0, symName: ___llvm_profile_get_dir_mode, symObjAddr: 0x5C, symBinAddr: 0x10005F520, symSize: 0xC }
  - { offset: 0x12303A, size: 0x8, addend: 0x0, symName: ___llvm_profile_set_dir_mode, symObjAddr: 0x68, symBinAddr: 0x10005F52C, symSize: 0xC }
  - { offset: 0x12304C, size: 0x8, addend: 0x0, symName: _lprofGetHostName, symObjAddr: 0x74, symBinAddr: 0x10005F538, symSize: 0x80 }
  - { offset: 0x123075, size: 0x8, addend: 0x0, symName: _lprofLockFd, symObjAddr: 0xF4, symBinAddr: 0x10005F5B8, symSize: 0x90 }
  - { offset: 0x1230DA, size: 0x8, addend: 0x0, symName: _lprofUnlockFd, symObjAddr: 0x184, symBinAddr: 0x10005F648, symSize: 0x90 }
  - { offset: 0x12312C, size: 0x8, addend: 0x0, symName: _lprofLockFileHandle, symObjAddr: 0x214, symBinAddr: 0x10005F6D8, symSize: 0x94 }
  - { offset: 0x1231AA, size: 0x8, addend: 0x0, symName: _lprofUnlockFileHandle, symObjAddr: 0x2A8, symBinAddr: 0x10005F76C, symSize: 0x94 }
  - { offset: 0x123218, size: 0x8, addend: 0x0, symName: _lprofOpenFileEx, symObjAddr: 0x33C, symBinAddr: 0x10005F800, symSize: 0xD4 }
  - { offset: 0x1232BD, size: 0x8, addend: 0x0, symName: _lprofGetPathPrefix, symObjAddr: 0x410, symBinAddr: 0x10005F8D4, symSize: 0x98 }
  - { offset: 0x123320, size: 0x8, addend: 0x0, symName: _lprofApplyPathPrefix, symObjAddr: 0x4A8, symBinAddr: 0x10005F96C, symSize: 0xAC }
  - { offset: 0x123341, size: 0x8, addend: 0x0, symName: _lprofFindFirstDirSeparator, symObjAddr: 0x554, symBinAddr: 0x10005FA18, symSize: 0x8 }
  - { offset: 0x12336A, size: 0x8, addend: 0x0, symName: _lprofFindLastDirSeparator, symObjAddr: 0x55C, symBinAddr: 0x10005FA20, symSize: 0x8 }
  - { offset: 0x123393, size: 0x8, addend: 0x0, symName: _lprofInstallSignalHandler, symObjAddr: 0x564, symBinAddr: 0x10005FA28, symSize: 0x58 }
  - { offset: 0x1233D8, size: 0x8, addend: 0x0, symName: _lprofReleaseMemoryPagesToOS, symObjAddr: 0x5C8, symBinAddr: 0x10005FA80, symSize: 0x58 }
...
