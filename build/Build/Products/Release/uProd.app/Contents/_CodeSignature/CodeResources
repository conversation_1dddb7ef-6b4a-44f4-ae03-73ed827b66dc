<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		SdHaMOTT+L8Et8eBJrAj3hbWCN4=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		J2tayzxX9BdRqm6EngPQjdhy/P4=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		ifwpvf9g5F+SWIEFddM/oYP8tQE=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/MainMenu.nib</key>
		<data>
		cu2mIOKpykwKSmnnrIX6WFP/AhM=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/NSWindowController-B8D-0N-5wS.nib</key>
		<data>
		Nfw553o00F/YOvub8q8N8Bni0C0=
		</data>
		<key>Resources/Base.lproj/Main.storyboardc/XfG-lQ-9wD-view-m2S-Jp-Qdl.nib</key>
		<data>
		dcwLIQgURpAxDkq7VP/MHVMjaZg=
		</data>
		<key>Resources/battery_level_1.png</key>
		<data>
		4Eh8/h3ipVmYk0cWP+0ygKQ5NnQ=
		</data>
		<key>Resources/battery_level_2.png</key>
		<data>
		1OC+ROjfO6a9GAk6JgTYxZyiLJg=
		</data>
		<key>Resources/battery_level_3.png</key>
		<data>
		TO0Ee5OFZPLW27JZdhcbyUQ+UKw=
		</data>
		<key>Resources/battery_level_4.png</key>
		<data>
		wJ+AWgByxLdZQU3Ad6/8Ye7/l0A=
		</data>
		<key>Resources/battery_original.png</key>
		<data>
		aQEy5lRuUo5AgVEm/bGT+Ss/AKA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			0IHyFDFNgnHmytzsIRFuUKKN98NUwFm09fnOIrbwXYM=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			qYaXHm+K3R+fxehPP6JZMf0Vfo5jxxXV8aMvvkCQ2SU=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			UUmoVwaLoJ0/z6qO8JbDz9isHKCMoK8LmMG3jwTaZg8=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Daij5C5QYazNtCaDaxdbSRWnTbsqw6wTKDgkePZx5mY=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/NSWindowController-B8D-0N-5wS.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			ruFkqRImB69UGv9DH4LTwdPebatIPBpJ21vtPC89HOo=
			</data>
		</dict>
		<key>Resources/Base.lproj/Main.storyboardc/XfG-lQ-9wD-view-m2S-Jp-Qdl.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			E5TydeWynoe7ikUTfd2H87wFEu4leIDPGUwzqgAM40o=
			</data>
		</dict>
		<key>Resources/battery_level_1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			D7FqxmF9SG/i7Qrn66dPsmI2mLnyw3wHk8jRWqH8SFU=
			</data>
		</dict>
		<key>Resources/battery_level_2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			bZOsDrHTJVPB9yDz841HdueOdO4xiuNYSKXv+Mh+V7I=
			</data>
		</dict>
		<key>Resources/battery_level_3.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/LrIHm8565AUFZ7n+jlJFwPfkasiMepBLqWJ6OooC8M=
			</data>
		</dict>
		<key>Resources/battery_level_4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			TOgoyJ3FZY2uAUbFd80LIhB2oKejw7cbyDUsBKPhUnE=
			</data>
		</dict>
		<key>Resources/battery_original.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ss7CfhFvUAocfkvymbHkmvkcmDoDM1M2TTlUT0lEgms=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
