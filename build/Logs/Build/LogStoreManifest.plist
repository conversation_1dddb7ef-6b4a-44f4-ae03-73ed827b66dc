<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>251353AD-CF69-4865-BB1B-E29C9CAC21C4</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>251353AD-CF69-4865-BB1B-E29C9CAC21C4.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>SimplePomodoroTest project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>SimplePomodoroTest</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project SimplePomodoroTest with scheme SimplePomodoroTest and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>774618087.08680999</real>
			<key>timeStoppedRecording</key>
			<real>774618087.44307101</real>
			<key>title</key>
			<string>Cleaning project SimplePomodoroTest with scheme SimplePomodoroTest and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>251353AD-CF69-4865-BB1B-E29C9CAC21C4</string>
		</dict>
		<key>328AFB72-2328-4F4E-BDE4-9FE3FAD6CEF7</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>328AFB72-2328-4F4E-BDE4-9FE3FAD6CEF7.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>4</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>SimplePomodoroTest project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>SimplePomodoroTest</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project SimplePomodoroTest with scheme SimplePomodoroTest and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>774618087.44542694</real>
			<key>timeStoppedRecording</key>
			<real>774618104.92809105</real>
			<key>title</key>
			<string>Building project SimplePomodoroTest with scheme SimplePomodoroTest and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>328AFB72-2328-4F4E-BDE4-9FE3FAD6CEF7</string>
		</dict>
	</dict>
</dict>
</plist>
