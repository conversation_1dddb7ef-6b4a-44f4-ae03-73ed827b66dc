# Логика анализа привычек в SimplePomodoroTest

## Обзор системы

Система анализа состоит из нескольких компонентов:

1. **StatisticsManager** - сбор и хранение данных о завершенных интервалах
2. **WorkPatternAnalyzer** - анализ паттернов работы и выявление проблем
3. **AnalysisWindow** - отображение результатов анализа

## Структура данных

### CompletedInterval
```swift
struct CompletedInterval: Codable {
    let date: Date        // Когда был завершен интервал
    let duration: TimeInterval  // Продолжительность (обычно 1500 сек = 25 мин)
}
```

### WorkPattern
```swift
struct WorkPattern {
    let averageIntervalsPerDay: Double    // Среднее количество интервалов в день
    let workingDaysPerWeek: Int          // Количество рабочих дней в неделю
    let consistencyScore: Double         // Стабильность (0-1)
    let averageStartTime: TimeInterval?  // Среднее время начала работы
    let riskFactors: [RiskFactor]        // Выявленные проблемы
    let recommendations: [Recommendation] // Рекомендации
}
```

## Типы проблем (RiskFactor)

### 1. Переработка (overwork)
- **Условие**: `averageIntervalsPerDay > 7` (более 7 интервалов в день)
- **Что означает**: Человек работает более 3.5 часов в день без достаточных перерывов
- **Пример**: 8-10 интервалов в день = 4-5 часов подряд

### 2. Непостоянство (inconsistency)
- **Условие**: `consistencyScore < 0.6` (низкая стабильность)
- **Что означает**: Большие колебания в количестве работы по дням
- **Пример**: Один день 10 интервалов, другой день 1 интервал

### 3. Поздний старт (lateStart)
- **Условие**: `averageHour > 12` (начало работы после полудня)
- **Что означает**: Человек систематически откладывает начало работы
- **Пример**: Начинает работать в 14:00-15:00

### 4. Риск выгорания (burnoutRisk)
- **Условие**: `intenseDays > 2 && restDays > 3`
- **Что означает**: Чередование интенсивной работы (>6 интервалов) с полными днями отдыха
- **Пример**: 3 дня по 8 интервалов, потом 4 дня полного отдыха

### 5. Прокрастинация (procrastination)
- **Условие**: `workingDaysPerWeek < 4` (менее 4 рабочих дней в неделю)
- **Что означает**: Человек работает менее половины дней недели
- **Пример**: Работает только 2-3 дня в неделю

## Рекомендации (Recommendation)

### 1. Снизить нагрузку (reduceWorkload)
- **Для**: переработки
- **Логика**: Предлагает снизить до 80% от текущего уровня, но не более 6 интервалов
- **Пример**: "Снизьте с 8 до 6 интервалов в день"

### 2. Увеличить стабильность (increaseConsistency)
- **Для**: непостоянства
- **Совет**: "Старайтесь работать примерно одинаковое количество интервалов каждый день"

### 3. Начинать раньше (startEarlier)
- **Для**: позднего старта
- **Логика**: Предлагает начинать на 2 часа раньше, но не раньше 9:00
- **Пример**: "Начинайте в 11:00 вместо 13:00"

### 4. Делать перерывы (takeBreaks)
- **Для**: риска выгорания
- **Совет**: "Обнаружен паттерн интенсивной работы с последующими длительными перерывами"

### 5. Установить рутину (establishRoutine)
- **Для**: прокрастинации
- **Совет**: "Попробуйте работать хотя бы по 1-2 интервала каждый день"

### 6. Поддерживать баланс (maintainBalance)
- **Для**: хороших показателей
- **Условие**: Нет рисков + стабильность > 0.8 + 3-6 интервалов в день
- **Совет**: "Отличная работа! Вы поддерживаете здоровый и стабильный рабочий ритм"

## Алгоритм анализа

### Шаг 1: Сбор данных
```swift
let intervals = getIntervalsForPeriod(period) // Получаем интервалы за период
```

### Шаг 2: Расчет базовых метрик
```swift
let dailyStats = calculateDailyStatistics(intervals)        // Группировка по дням
let averageIntervalsPerDay = calculateAverageIntervalsPerDay(dailyStats)
let workingDaysPerWeek = calculateWorkingDaysPerWeek(dailyStats)
let consistencyScore = calculateConsistencyScore(dailyStats)
let averageStartTime = calculateAverageStartTime(intervals)
```

### Шаг 3: Выявление проблем
```swift
let riskFactors = identifyRiskFactors(...)  // Проверяем каждый тип риска
```

### Шаг 4: Генерация рекомендаций
```swift
let recommendations = generateRecommendations(riskFactors: riskFactors, ...)
```

### Шаг 5: Создание результата
```swift
return WorkPattern(...)  // Объединяем все данные
```

## Примеры паттернов

### Здоровый паттерн
- 4-5 интервалов в день
- Начало в 9-11 утра
- 5-6 рабочих дней в неделю
- Стабильность > 0.8
- **Результат**: "Отличная работа! Поддерживайте баланс"

### Переработка
- 8-10 интервалов в день
- Начало в 13-14 часов
- Работа подряд без перерывов
- **Результат**: "Снизьте нагрузку, начинайте раньше"

### Прокрастинация
- 1-2 интервала в день
- 2-3 рабочих дня в неделю
- Нерегулярность
- **Результат**: "Установите рутину, работайте каждый день понемногу"

## Настройка демо-данных

В `AnalysisWindow.swift` функция `generateDemoData()` создает тестовые данные:

- **Текущие демо-данные**: Паттерн переработки с поздним стартом
- **80% дней**: Начало после 12:00
- **6-10 интервалов подряд**: Имитация работы без перерывов
- **14 дней данных**: Достаточно для выявления паттернов

Это должно вызвать риски:
1. `lateStart` - поздний старт
2. `overwork` - переработка (если >7 интервалов в день)
