import Cocoa

class ProjectManagementWindow: NSWindowController {
    
    // MARK: - Properties

    private var projectManager: ProjectManager
    private var mainTableView: NSTableView!
    private var mainScrollView: NSScrollView!
    private var createButton: NSButton!
    private var editButton: NSButton!
    private var deleteButton: NSButton!
    private var archiveButton: NSButton!
    private var unarchiveButton: NSButton!
    private var showArchivedButton: NSButton!

    // Data sources
    private var displayedProjects: [Project] = []
    private var showArchived = false
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager) {
        self.projectManager = projectManager
        
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 700, height: 500),
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)

        setupWindow()
        setupUI()
        refreshData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Window Setup
    
    private func setupWindow() {
        guard let window = window else { return }

        window.title = "Управление проектами"
        window.center()
        window.isReleasedWhenClosed = false
        window.delegate = self
        window.minSize = NSSize(width: 600, height: 400)
    }

    private func setupUI() {
        NSLog("🔧 ProjectManagementWindow: setupUI вызван")
        guard let window = window, let contentView = window.contentView else { return }

        // Создаем основную таблицу
        mainTableView = NSTableView()
        mainTableView.delegate = self
        mainTableView.dataSource = self
        mainTableView.headerView = nil
        mainTableView.rowSizeStyle = .medium

        // Настраиваем drag & drop
        mainTableView.registerForDraggedTypes([.string])
        mainTableView.setDraggingSourceOperationMask(.move, forLocal: true)

        // Создаем колонки
        let nameColumn = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("name"))
        nameColumn.title = "Название"
        nameColumn.width = 200
        mainTableView.addTableColumn(nameColumn)

        let favoriteColumn = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("favorite"))
        favoriteColumn.title = "⭐"
        favoriteColumn.width = 30
        mainTableView.addTableColumn(favoriteColumn)

        let typeColumn = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("type"))
        typeColumn.title = "Тип"
        typeColumn.width = 100
        mainTableView.addTableColumn(typeColumn)

        let statusColumn = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("status"))
        statusColumn.title = "Статус"
        statusColumn.width = 100
        mainTableView.addTableColumn(statusColumn)

        // Создаем scroll view
        mainScrollView = NSScrollView()
        mainScrollView.documentView = mainTableView
        mainScrollView.hasVerticalScroller = true
        mainScrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(mainScrollView)

        // Создаем кнопки
        let buttonContainer = NSView()
        buttonContainer.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(buttonContainer)

        createButton = NSButton(title: "Создать", target: self, action: #selector(createProject))
        createButton.translatesAutoresizingMaskIntoConstraints = false
        NSLog("🔧 ProjectManagementWindow: createButton создана, target: \(String(describing: createButton.target)), action: \(String(describing: createButton.action))")

        editButton = NSButton(title: "Редактировать", target: self, action: #selector(editProject))
        editButton.translatesAutoresizingMaskIntoConstraints = false

        deleteButton = NSButton(title: "Удалить", target: self, action: #selector(deleteProject))
        deleteButton.translatesAutoresizingMaskIntoConstraints = false

        archiveButton = NSButton(title: "Архивировать", target: self, action: #selector(archiveProject))
        archiveButton.translatesAutoresizingMaskIntoConstraints = false

        unarchiveButton = NSButton(title: "Разархивировать", target: self, action: #selector(unarchiveProject))
        unarchiveButton.translatesAutoresizingMaskIntoConstraints = false

        showArchivedButton = NSButton(title: "Показать архивные", target: self, action: #selector(toggleShowArchived))
        showArchivedButton.translatesAutoresizingMaskIntoConstraints = false

        buttonContainer.addSubview(createButton)
        buttonContainer.addSubview(editButton)
        buttonContainer.addSubview(deleteButton)
        buttonContainer.addSubview(archiveButton)
        buttonContainer.addSubview(unarchiveButton)
        buttonContainer.addSubview(showArchivedButton)

        // Constraints
        NSLayoutConstraint.activate([
            mainScrollView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            mainScrollView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            mainScrollView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            mainScrollView.bottomAnchor.constraint(equalTo: buttonContainer.topAnchor, constant: -20),

            buttonContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            buttonContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            buttonContainer.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            buttonContainer.heightAnchor.constraint(equalToConstant: 40),

            createButton.leadingAnchor.constraint(equalTo: buttonContainer.leadingAnchor),
            createButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            editButton.leadingAnchor.constraint(equalTo: createButton.trailingAnchor, constant: 10),
            editButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            deleteButton.leadingAnchor.constraint(equalTo: editButton.trailingAnchor, constant: 10),
            deleteButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            archiveButton.leadingAnchor.constraint(equalTo: deleteButton.trailingAnchor, constant: 10),
            archiveButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            unarchiveButton.leadingAnchor.constraint(equalTo: archiveButton.trailingAnchor, constant: 10),
            unarchiveButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            showArchivedButton.trailingAnchor.constraint(equalTo: buttonContainer.trailingAnchor),
            showArchivedButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor)
        ])
    }

    private func refreshData() {
        let allProjects = projectManager.getActiveProjects() + projectManager.getArchivedProjects()
        let favoriteIds = Set(projectManager.getFavoriteProjects().map { $0.id })

        // Сортируем проекты: сначала избранные, потом обычные, потом архивные
        var sortedProjects: [Project] = []

        // Добавляем избранные проекты (активные) в порядке добавления в избранное
        let activeFavorites = allProjects.filter {
            $0.isActive && !$0.isArchived && favoriteIds.contains($0.id)
        }
        // Сортируем избранные по алфавиту для стабильности
        sortedProjects.append(contentsOf: activeFavorites.sorted { $0.name < $1.name })

        // Добавляем остальные активные проекты
        let otherActiveProjects = allProjects.filter {
            $0.isActive && !$0.isArchived && !favoriteIds.contains($0.id)
        }
        sortedProjects.append(contentsOf: otherActiveProjects.sorted { $0.name < $1.name })

        // Добавляем архивные проекты, если нужно показывать
        if showArchived {
            let archivedProjects = allProjects.filter { $0.isArchived }
            sortedProjects.append(contentsOf: archivedProjects.sorted { $0.name < $1.name })
        }

        displayedProjects = sortedProjects
        mainTableView.reloadData()
        updateButtonStates()
    }

    private func updateButtonStates() {
        let selectedRow = mainTableView.selectedRow
        let hasSelection = selectedRow >= 0 && selectedRow < displayedProjects.count

        editButton.isEnabled = hasSelection
        deleteButton.isEnabled = hasSelection

        if hasSelection {
            let project = displayedProjects[selectedRow]
            archiveButton.isEnabled = !project.isArchived
            unarchiveButton.isEnabled = project.isArchived

            // Показываем/скрываем кнопки в зависимости от статуса проекта
            archiveButton.isHidden = project.isArchived
            unarchiveButton.isHidden = !project.isArchived
        } else {
            archiveButton.isEnabled = false
            unarchiveButton.isEnabled = false
            archiveButton.isHidden = false
            unarchiveButton.isHidden = true
        }

        showArchivedButton.title = showArchived ? "Скрыть архивные" : "Показать архивные"
    }

    // MARK: - Public Methods

    func showWindow() {
        refreshData()
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
}

// MARK: - Action Methods

extension ProjectManagementWindow {

    @objc private func createProject() {
        NSLog("🔧 ProjectManagementWindow: createProject вызван")
        let dialog = ProjectEditDialog(projectManager: projectManager) { [weak self] project in
            NSLog("🔧 ProjectManagementWindow: completion вызван с проектом: \(project?.name ?? "nil")")
            if project != nil {
                self?.refreshData()
                // Уведомляем об изменении проектов
                NotificationCenter.default.post(name: NSNotification.Name("FavoritesChanged"), object: nil)
            }
        }
        NSLog("🔧 ProjectManagementWindow: вызываем showDialog")
        dialog.showDialog()
        NSLog("🔧 ProjectManagementWindow: showDialog завершен")
    }

    @objc private func editProject() {
        let selectedRow = mainTableView.selectedRow
        guard selectedRow >= 0 && selectedRow < displayedProjects.count else { return }

        let project = displayedProjects[selectedRow]
        let dialog = ProjectEditDialog(projectManager: projectManager, project: project) { [weak self] updatedProject in
            if updatedProject != nil {
                self?.refreshData()
                // Уведомляем об изменении проектов
                NotificationCenter.default.post(name: NSNotification.Name("FavoritesChanged"), object: nil)
            }
        }
        dialog.showDialog()
    }

    @objc private func deleteProject() {
        let selectedRow = mainTableView.selectedRow
        guard selectedRow >= 0 && selectedRow < displayedProjects.count else { return }

        let project = displayedProjects[selectedRow]

        let alert = NSAlert()
        alert.messageText = "Удаление проекта"
        alert.informativeText = "Вы уверены, что хотите удалить проект '\(project.name)'?"
        alert.addButton(withTitle: "Удалить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .warning

        if alert.runModal() == .alertFirstButtonReturn {
            projectManager.deleteProject(project)
            refreshData()
        }
    }

    @objc private func archiveProject() {
        let selectedRow = mainTableView.selectedRow
        guard selectedRow >= 0 && selectedRow < displayedProjects.count else { return }

        let project = displayedProjects[selectedRow]
        projectManager.archiveProject(project)
        refreshData()
    }

    @objc private func unarchiveProject() {
        let selectedRow = mainTableView.selectedRow
        guard selectedRow >= 0 && selectedRow < displayedProjects.count else { return }

        let project = displayedProjects[selectedRow]
        projectManager.unarchiveProject(project)
        refreshData()
    }

    @objc private func toggleShowArchived() {
        showArchived.toggle()
        refreshData()
    }

    @objc private func favoriteButtonClicked(_ sender: NSButton) {
        let row = sender.tag
        guard row >= 0 && row < displayedProjects.count else { return }

        let project = displayedProjects[row]
        toggleFavorite(for: project)
    }

    private func toggleFavorite(for project: Project) {
        if projectManager.isFavorite(project) {
            projectManager.removeFromFavorites(project)
        } else {
            projectManager.addToFavorites(project)
        }
        refreshData()

        // Уведомляем AppDelegate об изменении избранных для обновления меню
        NotificationCenter.default.post(name: NSNotification.Name("FavoritesChanged"), object: nil)
    }
}

// MARK: - NSTableViewDataSource

extension ProjectManagementWindow: NSTableViewDataSource {

    func numberOfRows(in tableView: NSTableView) -> Int {
        return displayedProjects.count
    }

    // MARK: - Drag & Drop Support

    func tableView(_ tableView: NSTableView, pasteboardWriterForRow row: Int) -> NSPasteboardWriting? {
        guard row < displayedProjects.count else { return nil }

        let project = displayedProjects[row]
        let item = NSPasteboardItem()
        item.setString(project.id.uuidString, forType: .string)
        return item
    }

    func tableView(_ tableView: NSTableView, validateDrop info: NSDraggingInfo, proposedRow row: Int, proposedDropOperation dropOperation: NSTableView.DropOperation) -> NSDragOperation {
        // Разрешаем только перемещение между строками
        if dropOperation == .above {
            return .move
        }
        return []
    }

    func tableView(_ tableView: NSTableView, acceptDrop info: NSDraggingInfo, row: Int, dropOperation: NSTableView.DropOperation) -> Bool {
        guard let pasteboard = info.draggingPasteboard.string(forType: .string),
              let draggedProjectId = UUID(uuidString: pasteboard),
              let draggedIndex = displayedProjects.firstIndex(where: { $0.id == draggedProjectId }) else {
            return false
        }

        // Вычисляем новый индекс
        var newIndex = row
        if draggedIndex < row {
            newIndex -= 1
        }

        // Перемещаем проект в массиве
        let project = displayedProjects.remove(at: draggedIndex)
        displayedProjects.insert(project, at: newIndex)

        // Обновляем таблицу
        tableView.reloadData()

        // Выбираем перемещенную строку
        tableView.selectRowIndexes(IndexSet(integer: newIndex), byExtendingSelection: false)

        return true
    }
}

// MARK: - NSTableViewDelegate

extension ProjectManagementWindow: NSTableViewDelegate {

    func tableView(_ tableView: NSTableView, viewFor tableColumn: NSTableColumn?, row: Int) -> NSView? {
        guard row < displayedProjects.count else { return nil }

        let project = displayedProjects[row]
        let columnIdentifier = tableColumn?.identifier.rawValue ?? ""

        let cellView = NSTableCellView()

        switch columnIdentifier {
        case "name":
            let textField = NSTextField()
            textField.isEditable = false
            textField.isBordered = false
            textField.backgroundColor = .clear
            textField.stringValue = project.name
            textField.translatesAutoresizingMaskIntoConstraints = false

            cellView.addSubview(textField)
            cellView.textField = textField

            NSLayoutConstraint.activate([
                textField.leadingAnchor.constraint(equalTo: cellView.leadingAnchor, constant: 5),
                textField.trailingAnchor.constraint(equalTo: cellView.trailingAnchor, constant: -5),
                textField.centerYAnchor.constraint(equalTo: cellView.centerYAnchor)
            ])

        case "favorite":
            let isFavorite = projectManager.isFavorite(project)
            let favoriteButton = NSButton()
            favoriteButton.translatesAutoresizingMaskIntoConstraints = false
            favoriteButton.title = isFavorite ? "⭐" : "☆"
            favoriteButton.isBordered = false
            favoriteButton.target = self
            favoriteButton.action = #selector(favoriteButtonClicked(_:))
            favoriteButton.tag = row
            cellView.addSubview(favoriteButton)

            NSLayoutConstraint.activate([
                favoriteButton.centerXAnchor.constraint(equalTo: cellView.centerXAnchor),
                favoriteButton.centerYAnchor.constraint(equalTo: cellView.centerYAnchor)
            ])

        case "type":
            let textField = NSTextField()
            textField.isEditable = false
            textField.isBordered = false
            textField.backgroundColor = .clear
            textField.stringValue = "\(project.effectiveEmoji) \(project.type.displayName)"
            textField.translatesAutoresizingMaskIntoConstraints = false

            cellView.addSubview(textField)
            cellView.textField = textField

            NSLayoutConstraint.activate([
                textField.leadingAnchor.constraint(equalTo: cellView.leadingAnchor, constant: 5),
                textField.trailingAnchor.constraint(equalTo: cellView.trailingAnchor, constant: -5),
                textField.centerYAnchor.constraint(equalTo: cellView.centerYAnchor)
            ])

        case "status":
            let textField = NSTextField()
            textField.isEditable = false
            textField.isBordered = false
            textField.backgroundColor = .clear

            var statusText = ""
            if project.isArchived {
                statusText = "📦 Архив"
            } else if project.isActive {
                statusText = "✅ Активный"
            } else {
                statusText = "⏸️ Неактивный"
            }

            textField.stringValue = statusText
            textField.translatesAutoresizingMaskIntoConstraints = false

            cellView.addSubview(textField)
            cellView.textField = textField

            NSLayoutConstraint.activate([
                textField.leadingAnchor.constraint(equalTo: cellView.leadingAnchor, constant: 5),
                textField.trailingAnchor.constraint(equalTo: cellView.trailingAnchor, constant: -5),
                textField.centerYAnchor.constraint(equalTo: cellView.centerYAnchor)
            ])

        default:
            break
        }

        return cellView
    }

    func tableViewSelectionDidChange(_ notification: Notification) {
        updateButtonStates()
    }
}

// MARK: - NSWindowDelegate

extension ProjectManagementWindow: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        // Окно будет скрыто, но не освобождено из памяти
    }
}
