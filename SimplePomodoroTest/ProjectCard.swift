import Cocoa

protocol ProjectCardDelegate: AnyObject {
    func projectCardDidRequestEdit(_ card: ProjectCard)
    func projectCardDidRequestDelete(_ card: ProjectCard)
    func projectCardDidRequestToggleFavorite(_ card: ProjectCard)
    func projectCardDidStartDrag(_ card: ProjectCard)
    func projectCardDidEndDrag(_ card: ProjectCard, to targetCard: ProjectCard?)
}

class ProjectCard: NSView, NSDraggingSource {
    
    // MARK: - Properties
    
    weak var delegate: ProjectCardDelegate?
    private let project: Project
    private let projectManager: ProjectManager
    private let statisticsManager = StatisticsManager()
    var currentProject: Project { return project } // Для доступа к проекту извне
    
    private var containerView: NSView!
    private var gradientLayer: CAGradientLayer!
    private var emojiLabel: NSTextField!
    private var nameLabel: NSTextField!
    private var typeLabel: NSTextField!
    private var statsLabel: NSTextField!
    private var favoriteButton: NSButton!
    private var editButton: NSButton!
    private var deleteButton: NSButton!

    
    private var isHovered = false
    private var isDragging = false
    private var initialMouseLocation = NSPoint.zero
    
    // MARK: - Initialization
    
    init(project: Project, projectManager: ProjectManager) {
        self.project = project
        self.projectManager = projectManager
        super.init(frame: .zero)
        setupUI()
        setupTracking()
        setupDragAndDrop()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true

        // Включаем обработку событий мыши
        acceptsTouchEvents = true

        // Контейнер для карточки
        containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(containerView)
        
        setupGradientBackground()
        setupContent()
        setupConstraints()
        setupGestureRecognizers()
    }
    
    private func setupGradientBackground() {
        gradientLayer = CAGradientLayer()
        
        // Цвета на основе цвета проекта
        let projectColor = NSColor(hex: project.color ?? "#4A90E2") ?? NSColor.systemBlue
        let color1 = projectColor.withAlphaComponent(0.15)
        let color2 = projectColor.withAlphaComponent(0.08)
        let color3 = NSColor.black.withAlphaComponent(0.1)
        
        gradientLayer.colors = [color1.cgColor, color2.cgColor, color3.cgColor]
        gradientLayer.locations = [0.0, 0.5, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 12
        
        // Граница
        gradientLayer.borderColor = NSColor.white.withAlphaComponent(0.1).cgColor
        gradientLayer.borderWidth = 1
        
        // Тень
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.2
        gradientLayer.shadowOffset = CGSize(width: 0, height: 2)
        gradientLayer.shadowRadius = 8
        
        containerView.layer = gradientLayer
    }
    
    private func setupContent() {
        // Эмодзи проекта
        let emoji = project.customEmoji ?? project.type.emoji
        emojiLabel = NSTextField(labelWithString: emoji)
        emojiLabel.font = NSFont.systemFont(ofSize: 32)
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(emojiLabel)
        
        // Название проекта
        nameLabel = NSTextField(labelWithString: project.name)
        nameLabel.font = NSFont.systemFont(ofSize: 18, weight: .semibold)
        nameLabel.textColor = NSColor.white
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(nameLabel)
        
        // Тип проекта
        typeLabel = NSTextField(labelWithString: project.type.displayName)
        typeLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        typeLabel.textColor = NSColor.white.withAlphaComponent(0.7)
        typeLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(typeLabel)
        
        // Статистика
        statsLabel = NSTextField(labelWithString: "")
        statsLabel.font = NSFont.systemFont(ofSize: 12)
        statsLabel.textColor = NSColor.white.withAlphaComponent(0.5)
        statsLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(statsLabel)

        updateStatistics()
        
        // Кнопка избранного
        favoriteButton = NSButton()
        updateFavoriteButton() // Устанавливаем правильную иконку
        favoriteButton.isBordered = false
        favoriteButton.font = NSFont.systemFont(ofSize: 16)
        favoriteButton.target = self
        favoriteButton.action = #selector(favoriteClicked)
        favoriteButton.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(favoriteButton)
        
        // Кнопка редактирования
        editButton = NSButton()
        editButton.title = "✏️"
        editButton.isBordered = false
        editButton.font = NSFont.systemFont(ofSize: 14)
        editButton.target = self
        editButton.action = #selector(editClicked)
        editButton.translatesAutoresizingMaskIntoConstraints = false
        editButton.alphaValue = 1 // Показываем постоянно
        editButton.isEnabled = true // Убеждаемся, что кнопка активна
        editButton.wantsLayer = true
        containerView.addSubview(editButton)

        // Кнопка удаления
        deleteButton = NSButton()
        deleteButton.title = "🗑️"
        deleteButton.isBordered = false
        deleteButton.font = NSFont.systemFont(ofSize: 14)
        deleteButton.target = self
        deleteButton.action = #selector(deleteClicked)
        deleteButton.translatesAutoresizingMaskIntoConstraints = false
        deleteButton.alphaValue = 1 // Показываем постоянно
        deleteButton.isEnabled = true // Убеждаемся, что кнопка активна
        deleteButton.wantsLayer = true
        containerView.addSubview(deleteButton)


    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Контейнер
            containerView.topAnchor.constraint(equalTo: topAnchor),
            containerView.leadingAnchor.constraint(equalTo: leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: bottomAnchor),

            // Эмодзи в левом верхнем углу
            emojiLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            emojiLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),

            // Название проекта рядом с эмодзи
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            nameLabel.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 12),
            nameLabel.trailingAnchor.constraint(lessThanOrEqualTo: favoriteButton.leadingAnchor, constant: -16),

            // Тип проекта под названием с увеличенным отступом
            typeLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),
            typeLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            typeLabel.trailingAnchor.constraint(lessThanOrEqualTo: favoriteButton.leadingAnchor, constant: -16),

            // Статистика внизу с увеличенным отступом от типа проекта
            statsLabel.topAnchor.constraint(greaterThanOrEqualTo: typeLabel.bottomAnchor, constant: 4),
            statsLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12),
            statsLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            statsLabel.trailingAnchor.constraint(lessThanOrEqualTo: favoriteButton.leadingAnchor, constant: -16),

            // Кнопки в правой части, выровнены по центру вертикально
            // Порядок слева направо: избранное, редактирование, удаление
            favoriteButton.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            favoriteButton.trailingAnchor.constraint(equalTo: editButton.leadingAnchor, constant: -12),
            favoriteButton.widthAnchor.constraint(equalToConstant: 24),
            favoriteButton.heightAnchor.constraint(equalToConstant: 24),

            editButton.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            editButton.trailingAnchor.constraint(equalTo: deleteButton.leadingAnchor, constant: -12),
            editButton.widthAnchor.constraint(equalToConstant: 24),
            editButton.heightAnchor.constraint(equalToConstant: 24),

            deleteButton.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            deleteButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            deleteButton.widthAnchor.constraint(equalToConstant: 24),
            deleteButton.heightAnchor.constraint(equalToConstant: 24)
        ])
    }
    
    private func setupTracking() {
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
    
    private func setupDragAndDrop() {
        // Регистрируем типы для drag & drop
        registerForDraggedTypes([.string])

        // Включаем поддержку drag & drop
        wantsLayer = true
    }
    
    // MARK: - Mouse Events
    
    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)
        isHovered = true
        animateHoverState(true)
    }
    
    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)
        isHovered = false
        animateHoverState(false)
    }

    private func updateStatistics() {
        // Получаем статистику по проекту
        let intervals = statisticsManager.getIntervalsForProject(project.id)
        let totalIntervals = intervals.count
        let totalHours = Double(totalIntervals) * 52.0 / 60.0 // 52 минуты на интервал

        // Форматируем дату создания
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        let createdDate = formatter.string(from: project.createdAt)

        // Определяем статус проекта
        let status = getProjectStatus()

        // Формируем текст статистики
        var statsText = ""

        if totalIntervals > 0 {
            let hoursText = String(format: "%.1f ч", totalHours)
            statsText = "\(totalIntervals) сессий • \(hoursText)"
        } else {
            statsText = "Нет сессий"
        }

        statsText += " • \(createdDate)"

        if !status.isEmpty {
            statsText += " • \(status)"
        }

        statsLabel.stringValue = statsText
    }

    private func getProjectStatus() -> String {
        if project.isArchived {
            return "Завершен"
        } else if !project.isActive {
            return "Неактивен"
        } else if let lastUsed = project.lastUsedAt {
            let daysSinceLastUse = Calendar.current.dateComponents([.day], from: lastUsed, to: Date()).day ?? 0
            if daysSinceLastUse > 7 {
                return "Давно не использовался"
            }
        }
        return ""
    }

    private func setupGestureRecognizers() {
        // Убираем gesture recognizer, используем только mouseDown для лучшего контроля
        print("🖱️ Настройка обработки кликов для проекта: \(project.name)")
        NSLog("🖱️ Настройка обработки кликов для проекта: \(project.name)")
    }

    override func acceptsFirstMouse(for event: NSEvent?) -> Bool {
        print("🖱️ acceptsFirstMouse вызван для проекта: \(project.name)")
        NSLog("🖱️ acceptsFirstMouse вызван для проекта: \(project.name)")
        return true
    }

    override func mouseDown(with event: NSEvent) {
        print("🖱️ mouseDown вызван для проекта: \(project.name)")
        NSLog("🖱️ mouseDown вызван для проекта: \(project.name)")

        // Сохраняем начальную позицию мыши для drag & drop
        initialMouseLocation = convert(event.locationInWindow, from: nil)

        // Проверяем, что клик не по кнопкам
        let locationInView = convert(event.locationInWindow, from: nil)
        let clickedView = hitTest(locationInView)

        print("🖱️ Clicked view: \(String(describing: clickedView))")
        NSLog("🖱️ Clicked view: \(String(describing: clickedView))")

        // Если клик по кнопке, пропускаем обработку и позволяем кнопке обработать событие
        if clickedView == favoriteButton || clickedView == editButton || clickedView == deleteButton {
            print("🖱️ Клик по кнопке, передаем обработку кнопке")
            NSLog("🖱️ Клик по кнопке, передаем обработку кнопке")
            super.mouseDown(with: event)
            return
        }

        // НЕ открываем редактирование сразу - ждем mouseUp или mouseDragged
        print("🖱️ Ждем mouseUp или mouseDragged")
        NSLog("🖱️ Ждем mouseUp или mouseDragged")
    }

    override func mouseUp(with event: NSEvent) {
        print("🖱️ mouseUp вызван для проекта: \(project.name)")
        NSLog("🖱️ mouseUp вызван для проекта: \(project.name)")

        super.mouseUp(with: event)

        // Если не было перетаскивания, открываем редактирование
        if !isDragging {
            let locationInView = convert(event.locationInWindow, from: nil)
            let clickedView = hitTest(locationInView)

            // Проверяем, что клик по карточке (но не по кнопкам)
            if clickedView == self || clickedView == containerView || clickedView == emojiLabel ||
               clickedView == nameLabel || clickedView == typeLabel || clickedView == statsLabel {
                print("🎯 Открываем редактирование проекта: \(project.name)")
                NSLog("🎯 Открываем редактирование проекта: \(project.name)")

                // Анимация клика
                NSAnimationContext.runAnimationGroup { context in
                    context.duration = 0.1
                    containerView.animator().layer?.transform = CATransform3DMakeScale(0.98, 0.98, 1)
                } completionHandler: {
                    NSAnimationContext.runAnimationGroup { context in
                        context.duration = 0.1
                        self.containerView.animator().layer?.transform = CATransform3DIdentity
                    }
                }

                delegate?.projectCardDidRequestEdit(self)
            }
        }

        isDragging = false
    }

    // MARK: - Drag & Drop

    override func mouseDragged(with event: NSEvent) {
        print("🖱️ mouseDragged вызван для проекта: \(project.name)")
        NSLog("🖱️ mouseDragged вызван для проекта: \(project.name)")

        // Проверяем, что перетаскивание началось достаточно далеко от начальной точки
        let currentLocation = convert(event.locationInWindow, from: nil)
        let threshold: CGFloat = 10

        // Вычисляем расстояние от начальной точки
        let deltaX = abs(currentLocation.x - initialMouseLocation.x)
        let deltaY = abs(currentLocation.y - initialMouseLocation.y)

        print("🖱️ Delta: X=\(deltaX), Y=\(deltaY), threshold=\(threshold), isDragging=\(isDragging)")
        NSLog("🖱️ Delta: X=\(deltaX), Y=\(deltaY), threshold=\(threshold), isDragging=\(isDragging)")

        if (deltaX > threshold || deltaY > threshold) && !isDragging {
            print("🔄 Начинаем drag операцию для проекта: \(project.name)")
            NSLog("🔄 Начинаем drag операцию для проекта: \(project.name)")
            startDragOperation(with: event)
        }
    }

    private func startDragOperation(with event: NSEvent) {
        print("🔄 startDragOperation вызван для проекта: \(project.name)")
        NSLog("🔄 startDragOperation вызван для проекта: \(project.name)")

        guard !isDragging else {
            print("❌ Уже в процессе перетаскивания")
            NSLog("❌ Уже в процессе перетаскивания")
            return
        }

        isDragging = true
        print("✅ Устанавливаем isDragging = true")
        NSLog("✅ Устанавливаем isDragging = true")

        delegate?.projectCardDidStartDrag(self)
        print("✅ Вызвали delegate.projectCardDidStartDrag")
        NSLog("✅ Вызвали delegate.projectCardDidStartDrag")

        // Создаем изображение для перетаскивания
        let dragImage = createDragImage()
        print("✅ Создали dragImage: \(dragImage)")
        NSLog("✅ Создали dragImage: \(dragImage)")

        // Создаем NSDraggingItem с правильным размером
        let draggingItem = NSDraggingItem(pasteboardWriter: project.id.uuidString as NSString)
        draggingItem.setDraggingFrame(bounds, contents: dragImage)
        print("✅ Создали draggingItem с bounds: \(bounds)")
        NSLog("✅ Создали draggingItem с bounds: \(bounds)")

        // Начинаем операцию перетаскивания
        print("🚀 Начинаем beginDraggingSession")
        NSLog("🚀 Начинаем beginDraggingSession")
        beginDraggingSession(with: [draggingItem],
                           event: event,
                           source: self)
        print("✅ beginDraggingSession завершен")
        NSLog("✅ beginDraggingSession завершен")
    }

    private func createDragImage() -> NSImage {
        // Создаем изображение текущей карточки для перетаскивания
        let image = NSImage(size: bounds.size)
        image.lockFocus()

        // Рисуем полупрозрачную версию карточки
        layer?.render(in: NSGraphicsContext.current!.cgContext)

        image.unlockFocus()

        // Делаем изображение полупрозрачным
        let dragImage = NSImage(size: bounds.size)
        dragImage.lockFocus()
        image.draw(in: NSRect(origin: .zero, size: bounds.size),
                  from: NSRect(origin: .zero, size: bounds.size),
                  operation: .sourceOver,
                  fraction: 0.7)
        dragImage.unlockFocus()

        return dragImage
    }

    // MARK: - NSDraggingSource

    func draggingSession(_ session: NSDraggingSession, sourceOperationMaskFor context: NSDraggingContext) -> NSDragOperation {
        return .move
    }

    func draggingSession(_ session: NSDraggingSession, endedAt screenPoint: NSPoint, operation: NSDragOperation) {
        isDragging = false
        delegate?.projectCardDidEndDrag(self, to: nil)
    }

    // Убираем NSDraggingDestination из ProjectCard - будем обрабатывать в ProjectsViewController

    private func animateHoverState(_ hovered: Bool) {
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            if hovered {
                // Поднимаем карточку - кнопки остаются видимыми
                containerView.animator().layer?.transform = CATransform3DMakeTranslation(0, -4, 0)
                gradientLayer.shadowOpacity = 0.3
                gradientLayer.shadowRadius = 12
            } else {
                // Возвращаем в исходное состояние - кнопки остаются видимыми
                containerView.animator().layer?.transform = CATransform3DIdentity
                gradientLayer.shadowOpacity = 0.2
                gradientLayer.shadowRadius = 8
            }
        }
    }
    
    override func layout() {
        super.layout()
        gradientLayer.frame = containerView.bounds
    }

    private func updateFavoriteButton() {
        // Проверяем, в избранном ли проект через ProjectManager
        let isFavorite = projectManager.isFavorite(project)
        favoriteButton.title = isFavorite ? "⭐" : "☆"
        favoriteButton.toolTip = isFavorite ? "Убрать из избранного" : "Добавить в избранное"
    }
    
    // MARK: - Actions
    
    @objc private func favoriteClicked() {
        print("🌟 Favorite clicked for project: \(project.name)")
        NSLog("🌟 Favorite clicked for project: \(project.name)")

        // Анимация нажатия
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.1
            favoriteButton.animator().layer?.transform = CATransform3DMakeScale(1.2, 1.2, 1)
        } completionHandler: {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.1
                self.favoriteButton.animator().layer?.transform = CATransform3DIdentity
            }
        }

        // Переключаем избранное через делегат
        delegate?.projectCardDidRequestToggleFavorite(self)
    }

    @objc private func editClicked() {
        print("✏️ Edit clicked for project: \(project.name)")
        NSLog("✏️ Edit clicked for project: \(project.name)")
        print("✏️ Delegate: \(String(describing: delegate))")
        NSLog("✏️ Delegate: \(String(describing: delegate))")
        delegate?.projectCardDidRequestEdit(self)
    }

    @objc private func deleteClicked() {
        print("🗑️ Delete clicked for project: \(project.name)")
        NSLog("🗑️ Delete clicked for project: \(project.name)")
        delegate?.projectCardDidRequestDelete(self)
    }



    // MARK: - Stable Button Creation

    private func createStableButton(title: String, isPrimary: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true
        button.isEnabled = true
        button.bezelStyle = .rounded

        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        gradientLayer.cornerRadius = 8

        if isPrimary {
            // Зеленый градиент для основной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.3, alpha: 1.0).cgColor
            ]
        } else {
            // Серый градиент для вторичной кнопки
            gradientLayer.colors = [
                NSColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0).cgColor,
                NSColor(red: 0.3, green: 0.3, blue: 0.3, alpha: 1.0).cgColor
            ]
        }

        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)

        // Добавляем градиент как подслой, а не заменяем основной слой
        button.layer?.addSublayer(gradientLayer)

        // Белый текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: 14, weight: .medium)
            ]
        )

        // Обновляем размер градиента при изменении размера кнопки
        DispatchQueue.main.async {
            gradientLayer.frame = button.bounds
        }

        return button
    }
}
