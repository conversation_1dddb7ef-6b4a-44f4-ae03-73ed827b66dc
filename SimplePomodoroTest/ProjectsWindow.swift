import Cocoa

class ProjectsWindow: NSWindowController {
    
    // MARK: - Properties
    
    private let projectManager: ProjectManager
    private var projectsViewController: ProjectsViewController!
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager) {
        self.projectManager = projectManager
        
        // Создаем окно с современным дизайном
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 900, height: 700),
            styleMask: [.titled, .closable, .resizable, .fullSizeContentView],
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)
        
        setupWindow()
        setupViewController()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = "Управление проектами"
        window.center()
        window.isReleasedWhenClosed = false
        window.delegate = self
        window.minSize = NSSize(width: 800, height: 600)
        
        // Современный стиль окна
        window.titlebarAppearsTransparent = true
        window.titleVisibility = .hidden
        
        // Устанавливаем темную тему
        window.appearance = NSAppearance(named: .darkAqua)
        
        // Добавляем эффект размытия
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = .hudWindow
        visualEffectView.blendingMode = .behindWindow
        visualEffectView.state = .active
        
        window.contentView = visualEffectView
    }
    
    private func setupViewController() {
        projectsViewController = ProjectsViewController(projectManager: projectManager)
        
        guard let window = window,
              let contentView = window.contentView else { return }
        
        // Добавляем view controller
        let view = projectsViewController.view
        view.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(view)
        
        NSLayoutConstraint.activate([
            view.topAnchor.constraint(equalTo: contentView.topAnchor),
            view.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            view.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            view.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    // MARK: - Public Methods
    
    func showWindow() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
        
        // Анимация появления окна
        if let window = window {
            window.alphaValue = 0
            window.setFrame(window.frame.insetBy(dx: 20, dy: 20), display: true)
            
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.3
                context.timingFunction = CAMediaTimingFunction(name: .easeOut)
                window.animator().alphaValue = 1
                window.animator().setFrame(window.frame.insetBy(dx: -20, dy: -20), display: true)
            }
        }
    }
}

// MARK: - NSWindowDelegate

extension ProjectsWindow: NSWindowDelegate {
    
    func windowWillClose(_ notification: Notification) {
        // Анимация закрытия
        if let window = window {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.2
                window.animator().alphaValue = 0
                window.animator().setFrame(window.frame.insetBy(dx: 10, dy: 10), display: true)
            }
        }
    }
    
    func windowDidBecomeKey(_ notification: Notification) {
        // Обновляем данные при активации окна
        projectsViewController.viewDidLoad()
    }
}
