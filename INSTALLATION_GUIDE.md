# 🍅 SimplePomodoroTest - Руководство по установке

## 📦 Установка

### Способ 1: Готовое приложение (Рекомендуется)
1. **Скопируйте приложение** `SimplePomodoroTest.app` с рабочего стола в папку `/Applications`
2. **Запустите приложение** двойным кликом из папки Applications
3. **Найдите иконку 🍅** в системном трее (правая часть верхней панели macOS)

### Способ 2: Сборка из исходного кода
```bash
# Клонируйте репозиторий
git clone <your-repo-url>
cd SimplePomodoroTest

# Соберите релизную версию
xcodebuild -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -configuration Release clean build

# Скопируйте в Applications
cp -R ~/Library/Developer/Xcode/DerivedData/SimplePomodoroTest-*/Build/Products/Release/SimplePomodoroTest.app /Applications/
```

## 🚀 Первый запуск

### Автоматические настройки
При первом запуске приложение автоматически:
- ✅ **Включает автозапуск** при старте macOS
- ✅ **Запрашивает разрешения** на уведомления
- ✅ **Создает иконку** 🍅 в системном трее

### Что делать если иконка не появилась
1. **Проверьте системный трей** - иконка может быть скрыта
2. **Разверните скрытые иконки** - нажмите на стрелочки в трее
3. **Перезапустите приложение** из папки Applications

## ⚙️ Настройка

### Основные настройки
1. **Кликните на иконку 🍅** в системном трее
2. **Выберите "Настройки..."**
3. **Настройте параметры:**
   - **Длительность интервала**: 52 минуты (по умолчанию)
   - **Автозапуск при старте macOS**: ✅ Включен по умолчанию
   - **Режим тестирования**: Для быстрого тестирования (секунды вместо минут)

### Рекомендуемые настройки для тестирования
- **Установите 1 минуту** для быстрого тестирования
- **Включите режим тестирования** для работы в секундах
- **Проверьте автозапуск** - должен быть включен

## 📱 Использование

### Запуск интервала
1. **Кликните на 🍅** в системном трее
2. **Выберите "Начать интервал"**
3. **Работайте** - время отображается в трее
4. **Получите уведомление** по окончании интервала

### Функции приложения
- **📊 Статистика** - отслеживание рабочих интервалов
- **🔔 Уведомления** - полноэкранные напоминания о перерыве
- **⚡ Мотивация** - система поощрений за продуктивность
- **📈 Анализ паттернов** - анализ рабочих привычек

## 🔧 Автозапуск

### Управление автозапуском
- **Включить/выключить**: Настройки → "Запускать при старте macOS"
- **По умолчанию**: Автозапуск включен при первой установке
- **Проверка статуса**: Приложение автоматически синхронизирует настройки

### Если автозапуск не работает
1. **Откройте настройки** приложения
2. **Выключите и включите** автозапуск
3. **Перезагрузите Mac** для проверки
4. **Проверьте System Preferences** → Users & Groups → Login Items

## 🐛 Решение проблем

### Приложение не запускается
```bash
# Проверьте процессы
ps aux | grep SimplePomodoroTest

# Проверьте логи
log show --predicate 'process == "SimplePomodoroTest"' --last 5m
```

### Иконка не появляется в трее
1. **Проверьте разрешения** в System Preferences → Security & Privacy
2. **Перезапустите приложение** из Applications
3. **Проверьте скрытые иконки** в системном трее

### Уведомления не работают
1. **System Preferences** → Notifications & Focus
2. **Найдите SimplePomodoroTest** в списке
3. **Включите уведомления** и разрешите показ на экране блокировки

## 📋 Системные требования

- **macOS**: 15.5 или новее
- **Архитектура**: Apple Silicon (ARM64) или Intel (x86_64)
- **Разрешения**: Уведомления, Accessibility (опционально)

## 🔄 Обновление

1. **Замените файл** в папке Applications новой версией
2. **Перезапустите приложение**
3. **Настройки сохранятся** автоматически

## 📞 Поддержка

При возникновении проблем:
1. **Проверьте логи** системы
2. **Перезапустите приложение**
3. **Проверьте разрешения** в System Preferences
4. **Пересоберите** из исходного кода при необходимости

---

**Приятной работы с SimplePomodoroTest! 🍅**
