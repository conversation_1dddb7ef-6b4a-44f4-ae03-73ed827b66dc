# Руководство по статистике Pomodoro

## Обзор

В приложение SimplePomodoroTest добавлена система статистики для отслеживания полноценных интервалов Pomodoro. Статистика отслеживает только те интервалы, которые:

- Длились полные 52 минуты (не тестовые интервалы)
- Завершились естественным образом (таймер дошел до 0)
- Были завершены пользователем через окно уведомления (а не остановлены вручную)

## Как открыть статистику

1. Запустите приложение SimplePomodoroTest
2. Кликните на иконку приложения в строке меню (status bar)
3. В выпадающем меню выберите **"Статистика..."** или нажмите **Cmd+S**

## Что показывает статистика

### Основные показатели:
- **Сегодня**: количество полноценных интервалов за текущий день
- **Вчера**: количество интервалов за предыдущий день
- **Эта неделя**: количество интервалов с понедельника текущей недели
- **Этот месяц**: количество интервалов с начала текущего месяца
- **Всего**: общее количество записанных интервалов

### Таблица последних интервалов:
- Показывает до 10 последних полноценных интервалов
- Отображает дату, время и продолжительность каждого интервала
- Интервалы отсортированы по времени (новые сверху)

## Что НЕ учитывается в статистике

❌ **Тестовые интервалы** (запущенные через "Тест 10 сек")
❌ **Интервалы, остановленные вручную** (через "Остановить интервал")
❌ **Интервалы, продленные** в окне уведомления
❌ **Незавершенные интервалы**

## Что УЧИТЫВАЕТСЯ в статистике

✅ **Полноценные 52-минутные интервалы**
✅ **Естественно завершившиеся интервалы** (таймер дошел до 0)
✅ **Интервалы, завершенные через кнопку "Завершить"** в окне уведомления

## Хранение данных

- Статистика сохраняется в UserDefaults
- Данные сохраняются между запусками приложения
- Данные привязаны к конкретному пользователю macOS

## Обновление статистики

- Статистика обновляется автоматически при завершении полноценных интервалов
- В окне статистики есть кнопка **"Обновить"** для ручного обновления данных
- Окно статистики автоматически обновляется при открытии

## Примеры использования

### Ежедневное отслеживание продуктивности:
1. Запустите полноценный интервал (52 минуты)
2. Дождитесь окончания таймера
3. В окне уведомления нажмите "Завершить"
4. Откройте статистику, чтобы увидеть обновленные данные

### Анализ недельной продуктивности:
1. Откройте статистику в конце недели
2. Сравните показатели "Эта неделя" с предыдущими периодами
3. Используйте таблицу последних интервалов для анализа времени работы

## Технические детали

### Архитектура:
- **StatisticsManager**: управляет сохранением и получением данных
- **StatisticsWindow**: отображает статистику в удобном интерфейсе
- **PomodoroTimer**: отслеживает полноценные интервалы и уведомляет StatisticsManager

### Тестирование:
- Добавлены unit-тесты для StatisticsManager
- Добавлены тесты для интеграции с PomodoroTimer
- Все тесты проходят успешно

## Горячие клавиши

- **Cmd+S**: открыть окно статистики (когда меню приложения открыто)

## Устранение неполадок

**Проблема**: Статистика не обновляется
**Решение**: Убедитесь, что вы завершаете полноценные интервалы (не тестовые) через окно уведомления

**Проблема**: Пропали данные статистики
**Решение**: Данные хранятся в UserDefaults. Проверьте, не сбрасывались ли настройки приложения

**Проблема**: Неправильный подсчет интервалов
**Решение**: Помните, что учитываются только полноценные интервалы, завершенные естественным образом
