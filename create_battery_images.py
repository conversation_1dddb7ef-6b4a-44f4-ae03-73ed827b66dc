#!/usr/bin/env python3
"""
Скрипт для создания 4 уровней батарейки из оригинального изображения
"""

from PIL import Image, ImageDraw
import os

def create_battery_levels():
    # Путь к оригинальному изображению
    original_path = "SimplePomodoroTest/Resources/BatteryImages/battery_original.png"
    output_dir = "SimplePomodoroTest/Resources/BatteryImages"
    
    # Открываем оригинальное изображение
    try:
        original = Image.open(original_path)
        print(f"Оригинальное изображение: {original.size}")
    except Exception as e:
        print(f"Ошибка при открытии изображения: {e}")
        return
    
    # Конвертируем в RGBA если нужно
    if original.mode != 'RGBA':
        original = original.convert('RGBA')
    
    # Размеры для маленькой батарейки (подходящие для UI)
    target_width = 24
    target_height = 12
    
    # Изменяем размер оригинального изображения
    resized = original.resize((target_width, target_height), Image.Resampling.LANCZOS)
    
    # Создаем 4 уровня заряда
    levels = [
        ("battery_level_1.png", 0.25),  # 25% - красный
        ("battery_level_2.png", 0.50),  # 50% - оранжевый  
        ("battery_level_3.png", 0.75),  # 75% - желтый
        ("battery_level_4.png", 1.00),  # 100% - зеленый
    ]
    
    colors = [
        (255, 0, 0, 255),      # Красный
        (255, 165, 0, 255),    # Оранжевый
        (255, 255, 0, 255),    # Желтый
        (0, 255, 0, 255),      # Зеленый
    ]
    
    for i, (filename, level) in enumerate(levels):
        # Создаем копию изображения
        battery_img = resized.copy()
        
        # Создаем маску для заполнения
        # Предполагаем, что батарейка занимает примерно 80% ширины (оставляем место для "плюсика")
        fill_width = int((target_width * 0.8) * level)
        
        # Создаем overlay для цветного заполнения
        overlay = Image.new('RGBA', (target_width, target_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # Рисуем заполнение батарейки (примерно в центре, оставляя границы)
        margin_x = 2
        margin_y = 2
        fill_height = target_height - 2 * margin_y
        
        if fill_width > 0:
            draw.rectangle([
                margin_x, 
                margin_y, 
                margin_x + fill_width, 
                margin_y + fill_height
            ], fill=colors[i])
        
        # Накладываем цветное заполнение на батарейку
        battery_img = Image.alpha_composite(battery_img, overlay)
        
        # Сохраняем
        output_path = os.path.join(output_dir, filename)
        battery_img.save(output_path, "PNG")
        print(f"Создано: {output_path}")

if __name__ == "__main__":
    create_battery_levels()
