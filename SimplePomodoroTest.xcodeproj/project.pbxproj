// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		07C614992E27BDB200A653D2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 07C6147F2E27BDAF00A653D2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 07C614862E27BDAF00A653D2;
			remoteInfo = SimplePomodoroTest;
		};
		07C614A32E27BDB200A653D2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 07C6147F2E27BDAF00A653D2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 07C614862E27BDAF00A653D2;
			remoteInfo = SimplePomodoroTest;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		07C614872E27BDAF00A653D2 /* SimplePomodoroTest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SimplePomodoroTest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		07C614982E27BDB200A653D2 /* SimplePomodoroTestTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SimplePomodoroTestTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		07C614A22E27BDB200A653D2 /* SimplePomodoroTestUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SimplePomodoroTestUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		07C614892E27BDAF00A653D2 /* SimplePomodoroTest */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SimplePomodoroTest;
			sourceTree = "<group>";
		};
		07C6149B2E27BDB200A653D2 /* SimplePomodoroTestTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SimplePomodoroTestTests;
			sourceTree = "<group>";
		};
		07C614A52E27BDB200A653D2 /* SimplePomodoroTestUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SimplePomodoroTestUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		07C614842E27BDAF00A653D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		07C614952E27BDB200A653D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		07C6149F2E27BDB200A653D2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		07C6147E2E27BDAF00A653D2 = {
			isa = PBXGroup;
			children = (
				07C614892E27BDAF00A653D2 /* SimplePomodoroTest */,
				07C6149B2E27BDB200A653D2 /* SimplePomodoroTestTests */,
				07C614A52E27BDB200A653D2 /* SimplePomodoroTestUITests */,
				07C614882E27BDAF00A653D2 /* Products */,
			);
			sourceTree = "<group>";
		};
		07C614882E27BDAF00A653D2 /* Products */ = {
			isa = PBXGroup;
			children = (
				07C614872E27BDAF00A653D2 /* SimplePomodoroTest.app */,
				07C614982E27BDB200A653D2 /* SimplePomodoroTestTests.xctest */,
				07C614A22E27BDB200A653D2 /* SimplePomodoroTestUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		07C614862E27BDAF00A653D2 /* SimplePomodoroTest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 07C614AC2E27BDB200A653D2 /* Build configuration list for PBXNativeTarget "SimplePomodoroTest" */;
			buildPhases = (
				07C614832E27BDAF00A653D2 /* Sources */,
				07C614842E27BDAF00A653D2 /* Frameworks */,
				07C614852E27BDAF00A653D2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				07C614892E27BDAF00A653D2 /* SimplePomodoroTest */,
			);
			name = SimplePomodoroTest;
			packageProductDependencies = (
			);
			productName = SimplePomodoroTest;
			productReference = 07C614872E27BDAF00A653D2 /* SimplePomodoroTest.app */;
			productType = "com.apple.product-type.application";
		};
		07C614972E27BDB200A653D2 /* SimplePomodoroTestTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 07C614AF2E27BDB200A653D2 /* Build configuration list for PBXNativeTarget "SimplePomodoroTestTests" */;
			buildPhases = (
				07C614942E27BDB200A653D2 /* Sources */,
				07C614952E27BDB200A653D2 /* Frameworks */,
				07C614962E27BDB200A653D2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				07C6149A2E27BDB200A653D2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				07C6149B2E27BDB200A653D2 /* SimplePomodoroTestTests */,
			);
			name = SimplePomodoroTestTests;
			packageProductDependencies = (
			);
			productName = SimplePomodoroTestTests;
			productReference = 07C614982E27BDB200A653D2 /* SimplePomodoroTestTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		07C614A12E27BDB200A653D2 /* SimplePomodoroTestUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 07C614B22E27BDB200A653D2 /* Build configuration list for PBXNativeTarget "SimplePomodoroTestUITests" */;
			buildPhases = (
				07C6149E2E27BDB200A653D2 /* Sources */,
				07C6149F2E27BDB200A653D2 /* Frameworks */,
				07C614A02E27BDB200A653D2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				07C614A42E27BDB200A653D2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				07C614A52E27BDB200A653D2 /* SimplePomodoroTestUITests */,
			);
			name = SimplePomodoroTestUITests;
			packageProductDependencies = (
			);
			productName = SimplePomodoroTestUITests;
			productReference = 07C614A22E27BDB200A653D2 /* SimplePomodoroTestUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		07C6147F2E27BDAF00A653D2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					07C614862E27BDAF00A653D2 = {
						CreatedOnToolsVersion = 16.4;
					};
					07C614972E27BDB200A653D2 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 07C614862E27BDAF00A653D2;
					};
					07C614A12E27BDB200A653D2 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 07C614862E27BDAF00A653D2;
					};
				};
			};
			buildConfigurationList = 07C614822E27BDAF00A653D2 /* Build configuration list for PBXProject "SimplePomodoroTest" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 07C6147E2E27BDAF00A653D2;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 07C614882E27BDAF00A653D2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				07C614862E27BDAF00A653D2 /* SimplePomodoroTest */,
				07C614972E27BDB200A653D2 /* SimplePomodoroTestTests */,
				07C614A12E27BDB200A653D2 /* SimplePomodoroTestUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		07C614852E27BDAF00A653D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		07C614962E27BDB200A653D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		07C614A02E27BDB200A653D2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		07C614832E27BDAF00A653D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		07C614942E27BDB200A653D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		07C6149E2E27BDB200A653D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		07C6149A2E27BDB200A653D2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 07C614862E27BDAF00A653D2 /* SimplePomodoroTest */;
			targetProxy = 07C614992E27BDB200A653D2 /* PBXContainerItemProxy */;
		};
		07C614A42E27BDB200A653D2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 07C614862E27BDAF00A653D2 /* SimplePomodoroTest */;
			targetProxy = 07C614A32E27BDB200A653D2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		07C614AA2E27BDB200A653D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		07C614AB2E27BDB200A653D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		07C614AD2E27BDB200A653D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SimplePomodoroTest/SimplePomodoroTest.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.local.uProd;
				PRODUCT_NAME = uProd;
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		07C614AE2E27BDB200A653D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SimplePomodoroTest/SimplePomodoroTest.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.local.uProd;
				PRODUCT_NAME = uProd;
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		07C614B02E27BDB200A653D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.local.SimplePomodoroTestTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SimplePomodoroTest.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SimplePomodoroTest";
			};
			name = Debug;
		};
		07C614B12E27BDB200A653D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.local.SimplePomodoroTestTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SimplePomodoroTest.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SimplePomodoroTest";
			};
			name = Release;
		};
		07C614B32E27BDB200A653D2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.local.SimplePomodoroTestUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = SimplePomodoroTest;
			};
			name = Debug;
		};
		07C614B42E27BDB200A653D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.local.SimplePomodoroTestUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = SimplePomodoroTest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		07C614822E27BDAF00A653D2 /* Build configuration list for PBXProject "SimplePomodoroTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				07C614AA2E27BDB200A653D2 /* Debug */,
				07C614AB2E27BDB200A653D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		07C614AC2E27BDB200A653D2 /* Build configuration list for PBXNativeTarget "SimplePomodoroTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				07C614AD2E27BDB200A653D2 /* Debug */,
				07C614AE2E27BDB200A653D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		07C614AF2E27BDB200A653D2 /* Build configuration list for PBXNativeTarget "SimplePomodoroTestTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				07C614B02E27BDB200A653D2 /* Debug */,
				07C614B12E27BDB200A653D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		07C614B22E27BDB200A653D2 /* Build configuration list for PBXNativeTarget "SimplePomodoroTestUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				07C614B32E27BDB200A653D2 /* Debug */,
				07C614B42E27BDB200A653D2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 07C6147F2E27BDAF00A653D2 /* Project object */;
}
