#!/usr/bin/swift

import Foundation
import Cocoa

print("🔍 Детальное тестирование новых функций uProd...")

// Проверяем, что приложение запущено
func checkAppRunning() -> <PERSON><PERSON> {
    let runningApps = NSWorkspace.shared.runningApplications
    return runningApps.contains { $0.bundleIdentifier == "com.local.uProd" }
}

if checkAppRunning() {
    print("✅ uProd запущен")
} else {
    print("❌ uProd не запущен")
    exit(1)
}

// Функция для проверки UserDefaults
func checkUserDefaults() {
    print("\n📊 Проверка UserDefaults:")
    
    let defaults = UserDefaults.standard
    
    // Проверяем проекты
    if let projectsData = defaults.data(forKey: "projects") {
        print("✅ Данные проектов: \(projectsData.count) bytes")
        
        // Пытаемся декодировать
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let projects = try decoder.decode([ProjectData].self, from: projectsData)
            print("   📁 Найдено проектов: \(projects.count)")
            
            for project in projects {
                let workType = project.isWorkRelated ? "💼" : "🏠"
                print("   - \(project.type.emoji) \(project.name) \(workType)")
            }
        } catch {
            print("   ❌ Ошибка декодирования проектов: \(error)")
        }
    } else {
        print("❌ Данные проектов не найдены")
    }
    
    // Проверяем избранное
    if let favoritesData = defaults.data(forKey: "favoriteProjects") {
        print("✅ Данные избранного: \(favoritesData.count) bytes")
    } else {
        print("❌ Данные избранного не найдены")
    }
    
    // Проверяем интервалы
    if let intervalsData = defaults.data(forKey: "completedIntervals") {
        print("✅ Данные интервалов: \(intervalsData.count) bytes")
    } else {
        print("❌ Данные интервалов не найдены")
    }
}

// Структуры для декодирования (упрощенные версии)
struct ProjectData: Codable {
    let id: UUID
    let name: String
    let type: ProjectTypeData
    let isWorkRelated: Bool
    let color: String?
    let isActive: Bool
    let isArchived: Bool
}

enum ProjectTypeData: String, Codable, CaseIterable {
    case work = "work"
    case personal = "personal"
    case learning = "learning"
    case health = "health"
    case creative = "creative"
    
    var emoji: String {
        switch self {
        case .work: return "💼"
        case .personal: return "🏠"
        case .learning: return "📚"
        case .health: return "💪"
        case .creative: return "🎨"
        }
    }
}

// Функция для симуляции создания проекта
func simulateProjectCreation() {
    print("\n🛠️ Симуляция создания проекта...")
    
    // Создаем тестовый проект
    let testProject = ProjectData(
        id: UUID(),
        name: "Тестовый проект",
        type: .work,
        isWorkRelated: true,
        color: "#FF0000",
        isActive: true,
        isArchived: false
    )
    
    print("✅ Создан тестовый проект: \(testProject.type.emoji) \(testProject.name)")
    print("   - Рабочий: \(testProject.isWorkRelated ? "Да" : "Нет")")
    print("   - Цвет: \(testProject.color ?? "По умолчанию")")
}

// Функция для проверки меню
func checkMenuItems() {
    print("\n🍎 Проверка элементов меню...")
    
    // Получаем главное меню
    if let mainMenu = NSApp.mainMenu {
        print("✅ Главное меню доступно")
        
        // Ищем наше меню
        for menuItem in mainMenu.items {
            if menuItem.title.contains("uProd") || menuItem.title.contains("Начать") {
                print("✅ Найден пункт меню: \(menuItem.title)")
                
                if let submenu = menuItem.submenu {
                    for subItem in submenu.items {
                        print("   - \(subItem.title)")
                    }
                }
            }
        }
    } else {
        print("❌ Главное меню недоступно")
    }
}

// Запускаем проверки
checkUserDefaults()
simulateProjectCreation()
checkMenuItems()

print("\n🎯 Рекомендации для тестирования:")
print("1. Откройте меню в строке меню (иконка ⏱️)")
print("2. Проверьте, есть ли цветовые индикаторы рядом с проектами")
print("3. Откройте 'Управление проектами' и создайте новый проект")
print("4. Проверьте диалог создания проекта:")
print("   - Поле названия")
print("   - Выбор типа проекта")
print("   - Выбор цвета")
print("   - Чекбокс 'Рабочий проект'")
print("5. Запустите интервал и проверьте статистику")

print("\n🎉 Детальный тест завершен!")
